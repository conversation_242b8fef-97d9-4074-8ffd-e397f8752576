#!/usr/bin/env tsx
// 本地队列处理脚本

import { MatchingServiceV2 } from '../src/lib/services/matching-v2';
import { validateRequiredEnvVars } from '../src/lib/utils/env';

async function processQueue() {
  try {
    console.log('🚀 启动队列处理器...');
    console.log('⏰ 时间:', new Date().toISOString());

    // 验证环境变量
    console.log('🔍 验证环境变量...');
    validateRequiredEnvVars();

    await MatchingServiceV2.processQueuedRequests();

    console.log('✅ 队列处理完成');
  } catch (error) {
    console.error('❌ 队列处理失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  processQueue();
}

export { processQueue };
