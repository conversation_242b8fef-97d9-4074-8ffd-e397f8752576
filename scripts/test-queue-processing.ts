#!/usr/bin/env tsx
// 队列处理测试脚本

import { config } from 'dotenv';
import { MatchingServiceV2 } from '../src/lib/services/matching-v2';

// 加载环境变量
config({ path: '.env.local' });

async function testQueueProcessing() {
  try {
    console.log('🧪 开始测试队列处理机制...');
    console.log('⏰ 时间:', new Date().toISOString());
    
    // 检查环境变量
    if (!process.env.DATABASE_URL) {
      throw new Error('DATABASE_URL 环境变量未设置');
    }
    
    console.log('✅ 环境变量检查通过');
    
    // 测试队列处理
    console.log('🔄 测试队列处理功能...');
    const result = await MatchingServiceV2.processQueuedRequests();
    
    console.log(`✅ 队列处理完成，处理了 ${result.processedCount} 个任务`);
    
    // 测试获取匹配统计
    console.log('📊 测试匹配统计功能...');
    const testUserId = 'test-user-id';
    
    try {
      const stats = await MatchingServiceV2.getMatchingStats(testUserId);
      console.log('✅ 匹配统计:', stats);
    } catch (error) {
      console.log('⚠️ 匹配统计测试失败（预期行为，因为用户不存在）');
      console.log('错误信息:', error instanceof Error ? error.message : '未知错误');
    }
    
    console.log('🎉 队列处理机制测试完成');
  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('DATABASE_URL')) {
        console.log('💡 提示: 请在 .env.local 中设置 DATABASE_URL');
      }
    }
    
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  testQueueProcessing();
}

export { testQueueProcessing };
