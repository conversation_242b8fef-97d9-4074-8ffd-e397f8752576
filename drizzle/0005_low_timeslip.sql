ALTER TABLE "match_candidates" ADD COLUMN "compatibility_score" integer DEFAULT 0 NOT NULL;--> statement-breakpoint
ALTER TABLE "match_candidates" ADD COLUMN "reasoning" text;--> statement-breakpoint
ALTER TABLE "match_candidates" ADD COLUMN "highlights" jsonb;--> statement-breakpoint
ALTER TABLE "match_candidates" ADD COLUMN "challenges" jsonb;--> statement-breakpoint
ALTER TABLE "match_candidates" ADD COLUMN "personality_summary" text;--> statement-breakpoint
ALTER TABLE "match_candidates" ADD COLUMN "relationship_insight" jsonb;--> statement-breakpoint
ALTER TABLE "match_candidates" ADD COLUMN "conversation_simulation" jsonb;--> statement-breakpoint
ALTER TABLE "match_candidates" ADD COLUMN "date_plan" jsonb;--> statement-breakpoint
ALTER TABLE "match_requests" DROP COLUMN "final_report";