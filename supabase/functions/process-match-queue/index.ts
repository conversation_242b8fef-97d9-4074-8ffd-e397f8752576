// Supabase Edge Function: 处理匹配队列
// 这个函数将被 Supabase Cron Jobs 定期调用

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // 处理 CORS 预检请求
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('🔄 Supabase Edge Function: 开始处理匹配队列')
    
    // 获取环境变量
    const NEXT_APP_URL = Deno.env.get('NEXT_APP_URL') || 'http://localhost:3001'
    const WORKER_SECRET_TOKEN = Deno.env.get('WORKER_SECRET_TOKEN')
    
    // 调用 Next.js 应用的队列处理 API
    const response = await fetch(`${NEXT_APP_URL}/api/worker/process-queue`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': WORKER_SECRET_TOKEN ? `Bearer ${WORKER_SECRET_TOKEN}` : '',
      },
    })

    if (!response.ok) {
      throw new Error(`队列处理失败: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()
    console.log('✅ 队列处理完成:', result)

    return new Response(
      JSON.stringify({
        success: true,
        message: '队列处理完成',
        result,
        timestamp: new Date().toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('❌ Edge Function 执行失败:', error)
    
    return new Response(
      JSON.stringify({
        error: 'EDGE_FUNCTION_ERROR',
        message: error instanceof Error ? error.message : '未知错误',
        timestamp: new Date().toISOString()
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      },
    )
  }
})
