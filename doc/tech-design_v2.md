## 寡佬 AI v2.0：产品与技术规格书

**版本：** 2.0 (最终版)
**日期：** 2025年7月1日
**状态：** 已确认，可用于开发

### **Part I: 产品需求文档 (PRD)**

#### 1\. 版本摘要与核心升级

寡佬 AI v2.0 的核心是一次对匹配体验的颠覆性升级。我们正式从 v1.0 的“单一深度匹配”模式，进化为全新的 **“智能候选人矩阵 (Intelligent Candidate Matrix)”** 模式。

当用户发起一次匹配请求，强大的“红娘 Agent”将代替用户进行深度分析，并最终呈现一个包含 **5 名**高质量、经过排序的候选人列表。此举旨在赋予用户更广泛的选择权、更高的匹配成功率和更强的探索乐趣，从而彻底巩固“寡佬 AI”作为深度、智能社交应用的领导地位。

#### 2\. 核心功能：智能候选人矩阵

当一次匹配请求处理完成后，用户将收到一份包含 5 名候选人的报告，其内容结构经过精心设计，分为两个层级：

  * **1 x 首席推荐报告 (Full Report):**

      * **对象:** 经 `ARAG-Soul` 引擎综合评定后，与用户匹配度最高的**第 1 名**候选人。
      * **内容:**
          * 双方的详细人格摘要。
          * 量化的最终兼容性分数。
          * 深度的关系解读（包含潜在的优点、挑战和相处建议）。
          * **一段高质量、符合双方人设的多轮模拟对话。**
          * **一个基于双方共同兴趣的个性化初次约会计划。**

  * **4 x 潜力候选人简报 (Lite Report):**

      * **对象:** 综合排名第 2 至第 5 名的候选人。
      * **内容:**
          * 对方的简版人格摘要。
          * 量化的最终兼容性分数。
          * 关系亮点提炼（1-2个最核心的共鸣点）。
          * *(为突出重点和优化成本，简报不包含模拟对话和约会计划)*。

#### 3\. 用户流程

```mermaid
graph TD
    A[用户在主界面发起新匹配请求] --> B{检查配额};
    B -- 有配额 --> C["系统提示：'专属红娘正在为您筛选...'<br/>(后台异步处理开始)"];
    C --> D["等待... (用户可离开页面)"];
    D --> E["收到通知：'您的专属推荐已生成！'"];
    E --> F["打开全新的'候选人矩阵'结果页"];
    F --> G["查看并与5名候选人报告互动"];
    G --> H{"对候选人进行'喜欢'或'跳过'决策"};
    H -- 双方均喜欢 --> I["连接成功，开启真实聊天"];
    H -- 其他情况 --> A;
```

#### 4\. UI/UX 设计概念

  * **整体布局:** 一个垂直滚动的、经过精美设计的排名列表。
  * **首席推荐 (Top 1):** 在页面顶部以最大、最醒目的卡片呈现，并附有“首席推荐”徽章。卡片可展开，以沉浸式体验阅读完整的深度报告。
  * **潜力候选人 (Top 2-5):** 在“首席推荐”下方，以统一、紧凑的卡片样式按序列出。点击每张卡片可查看其“简报”内容。
  * **交互:** 每张卡片上都必须有独立的“喜欢”与“跳过”按钮，允许用户对 5 名候选人分别做出决策。

### **Part II: 技术设计文档 (TDD)**

#### 5\. 整体架构与技术栈

v2.0 延续并扩展了 v1.0 的技术栈，核心保持不变：

  * **AI 模型:** Google Gemini API
  * **Web 框架:** Next.js (全栈应用)
  * **数据存储:** Supabase (PostgreSQL)
  * **ORM:** Drizzle ORM
  * **流程编排:** **LangGraph.js**
  * **异步处理:** **Supabase Cron Jobs & Edge Functions**
  * **部署:** Vercel

#### 6\. 核心引擎技术方案：ARAG-Soul on LangGraph.js

**A. 流程编排 (Orchestration):**
我们将使用 **LangGraph.js** 来编排 `ARAG-Soul` 的多智能体工作流。这使得复杂的匹配逻辑变得结构化、可维护和可测试。

**B. Agent 节点核心逻辑:**

每个 Agent 都是一个函数，接收当前状态，执行任务，并返回一个部分更新的状态对象。

retrieveCandidatesNode

输入: userId, userProfile

动作:

使用 Drizzle ORM 从 user_profiles 表获取用户的核心描述文本。


在 user_profiles 表中高效地检索出 N 个（例如 N=50）语义上最相关的候选人。同时过滤掉已匹配、不活跃或不符合基本偏好的用户。

输出: { candidatePoolIds: [...] }

generateUserSoulProfileNode

输入: userProfile

动作: 调用 AI，传入用户完整资料，根据精心设计的 Prompt 生成“人格洞察 Agent”的输出——即用户的“灵魂画像”。

输出: { userSoulProfile: "..." }

runCompatibilityInferenceNode

输入: userSoulProfile, candidatePoolIds

动作:

从数据库批量获取 candidatePoolIds 对应的完整用户资料。

并行处理： 对每个候选人，构建一个包含“用户灵魂画像”和“候选人资料”的 Prompt。

并行调用 Gemini API，执行“深度兼容性推理”，为每个候选人生成{ score: number, reasoning: string, highlights: string[] }。

输出: { candidatesWithAnalysis: [...] }

rankAndFinalizeNode

输入: candidatesWithAnalysis

动作:

在内存中对所有候选人根据 score 进行降序排序。

选出 Top 5。

对 Top 1 候选人，额外构建 Prompt 调用 Gemini API，生成模拟对话和约会计划。

将 Top 1 的完整报告和 Top 2-5 的简报组装成最终的 JSON 结构。

输出: { finalMatrix: { topMatch: {...}, potentialMatches: [...] } }

#### 7\. 异步处理架构：Supabase 任务队列

为避免 API 超时并提升用户体验，整个匹配流程将异步执行。

**流程图:**

```mermaid
graph TD
    subgraph "即时响应"
        A[API Route: /api/matches/generate-matrix] -- 1. 接收请求 --> B[DB Table: match_queue];
        B -- 2. 任务入队 --> B;
        A -- 3. 立即返回 requestId --> C[用户端];
    end

    subgraph "后台异步处理"
        D[Supabase Cron Job] -- 4. 每分钟触发 --> E[Supabase Edge Function (Worker)];
        E -- 5. 从队列拉取任务 --> B;
        E -- 6. 调用 LangGraph 执行 --> F[LangGraph Engine];
        F -- 7. 将结果写入 DB --> G[DB Table: match_requests];
    end
```

**步骤详解:**

1.  **触发:** 前端调用 `/api/matches/generate-matrix`。
2.  **入队:** 该 API 路由不执行实际工作，仅在 `match_queue` 表中创建一条任务记录，然后立即返回 `202 Accepted` 和 `requestId`。
3.  **执行:** Supabase Cron Job 定期（如每分钟）触发一个 Edge Function (worker)。
4.  **处理:** Worker 从 `match_queue` 拉取待处理任务，调用 LangGraph 引擎执行完整的匹配流程。
5.  **完成:** LangGraph 执行完毕后，Worker 将最终的报告 JSON 存入 `match_requests` 表，并更新任务状态。
6.  **通知:** 客户端通过 Supabase Realtime 实时监听 `match_requests` 表的变更，一旦完成即可自动刷新界面展示结果。

#### 8\. 数据模型

为支持 v2.0，新增以下三张核心数据表：

```typescript
// /lib/db/schema.ts (v2.0 新增表)

// 1. 任务队列，用于异步处理匹配请求
export const matchQueue = pgTable('match_queue', {
  id: uuid('id').primaryKey().defaultRandom(),
  matchRequestId: uuid('match_request_id').notNull().unique(),
  requesterId: uuid('requester_id').references(() => users.id).notNull(),
  status: text('status').default('pending'), // pending, processing, completed, failed
  attempts: integer('attempts').default(0),
  createdAt: timestamp('created_at').defaultNow(),
});

// 2. 匹配请求的总记录
export const matchRequests = pgTable('match_requests', {
  id: uuid('id').primaryKey().defaultRandom(),
  requesterId: uuid('requester_id').references(() => users.id).notNull(),
  status: text('status').default('processing'), // processing, completed, failed
  finalReport: jsonb('final_report'), // 存储最终的 1+4 完整报告 JSON
  errorMessage: text('error_message'),
  createdAt: timestamp('created_at').defaultNow(),
});

// 3. 用于记录用户对每个候选人的决策
export const matchCandidates = pgTable('match_candidates', {
  id: uuid('id').primaryKey().defaultRandom(),
  requestId: uuid('request_id').references(() => matchRequests.id).notNull(),
  candidateId: uuid('candidate_id').references(() => users.id).notNull(),
  rank: integer('rank').notNull(), // 1 to 5
  userDecision: text('user_decision').default('pending'), // liked, skipped
  createdAt: timestamp('created_at').defaultNow(),
});
```

#### 9\. 隔离部署与兼容性

本次 v2.0 升级与 v1.0 完全隔离，可安全部署：

  * **API 隔离:** v2.0 使用全新的 API 端点，不影响 v1.0。
  * **数据隔离:** v2.0 使用上述全新数据表，不触及 v1.0 的 `matches` 表。
  * **部署策略:** 可将 v2.0 后端代码直接部署。