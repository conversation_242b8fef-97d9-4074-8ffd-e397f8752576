## 产品文档：寡佬 AI (Gualao AI)

**版本：** 2.0
**日期：** 2025年7月1日
**变更类型：** **核心功能重大升级 - 匹配引擎 v2.0**
**负责人：** [填写你的名字/团队名]

### 1\. 变更摘要 (Change Summary)

寡佬 AI v2.0 的核心升级是将原有的“单一深度匹配”模式，进化为\*\*“智能候选人矩阵 (Intelligent Candidate Matrix)”\*\*模式。当用户发起一次匹配请求后，系统不再返回唯一的匹配对象，而是由“红娘 Agent”利用升级后的 `ARAG-Soul` 框架，为用户提供一个包含 **5 名**经过深度分析和排序的候选人列表。

这个矩阵包含 **1 份“首席推荐报告”**（最匹配者，信息最完整）和 **4 份“潜力候选人简报”**（信息简约），旨在为用户提供更丰富的选择、更高的匹配成功率和更强的探索乐趣，同时牢牢巩固产品“深度、智能、有内涵”的核心定位。

### 2\. 修订背景与目标 (Background & Goals)

**背景：** v1.0 的单一匹配模式虽然极致专注，但在用户体验上存在“要么全中，要么全失”的风险。用户单次等待后，若对唯一结果不满意，挫败感较强。

**目标：**

  * **提升用户选择权：** 让用户在一组高质量的候选人中进行比较和选择，降低单次匹配的失败风险。
  * **增加用户获得感：** 即使用户不喜欢首席推荐，也可能对其他候选人产生兴趣，提升单次请求的价值感和应用的粘性。
  * **提高匹配效率：** 在一次 Agent 工作流中，最大化利用 AI 分析结果，提高找到至少一个潜在匹配对象的概率。
  * **增强探索与对比：** 用户可以通过对比不同候选人的报告，更清晰地了解自己的偏好和 AI 的匹配逻辑。

### 3\. 核心功能修订：匹配引擎 v2.0

原 `3.3 “红娘 Agent”驱动的智能匹配与分析引擎` 现升级为：

#### **3.3 “红娘 Agent”驱动的智能候选人矩阵 (Intelligent Candidate Matrix Powered by "Hongniang Agent")**

  * **描述：** 这是寡佬 AI v2.0 的核心引擎。当用户发起匹配请求，“红娘 Agent”会激活基于 `ARAG-Soul` 框架的多智能体系统，对候选人池进行全面的分析、推理、排序，并最终生成一个由 5 名候选人组成的、信息层级分明的推荐矩阵。

  * **智能体工作流调整：**

    1.  **人格洞察 Agent (Personality Insight Agent):** (职责不变) 生成发起方用户的“灵魂画像”。
    2.  **深度兼容性推理 Agent (Deep Compatibility Inference Agent):** (职责扩展) 对候选人池中的**所有**潜在对象进行深度推理和打分，形成一个包含分数和详细理由的完整列表。
    3.  **关系亮点提炼 Agent (Relationship Highlight Agent):** (职责扩展) 为所有得分**超过基础阈值**的候选人组合，都生成一份“关系亮点”和“潜在挑战”的摘要。
    4.  **“红娘”决策与排序 Agent (Hongniang Decision & Ranking Agent):** (职责升级)
          * **综合排序：** 整合所有候选人的分数、推理依据和关系亮点，进行一次复杂的加权排序。
          * **筛选矩阵：** 从排序结果中，选出**排名前 5** 的候选人，构成此次推荐的最终矩阵。
          * **分发任务：**
              * 向内容生成模块下达指令，为**排名第 1** 的“首席推荐”生成完整的深度内容，包括**模拟对话**和**个性化约会计划**。
              * 为**排名第 2-5** 的“潜力候选人”打包其已有的分析结果（分数、亮点等），形成简报。

  * **输出内容结构：**

      * **1 x 首席推荐报告 (Full Report):**
          * 人格摘要 (双方)
          * 最终兼容性分数 (例如：92分)
          * 深度关系解读 (优点、潜在挑战)
          * **多轮模拟对话**
          * **个性化约会计划**
      * **4 x 潜力候选人简报 (Lite Report):**
          * 人格摘要 (仅对方)
          * 最终兼容性分数 (例如：88分, 85分, ...)
          * 关系亮点提炼 (例如：1-2个最核心的共同点或互补点)
          * *(不包含模拟对话和约会计划，以节约成本和突出重点)*

### 4\. 匹配报告呈现 (Match Report Presentation - UI/UX)

当匹配结果生成后，用户将看到一个全新的结果页面：

  * **整体布局：** 一个垂直滚动的、经过精心设计的 ranked list。
  * **首席推荐 (Top 1):**
      * 以最大、最突出卡片的形式置于页面顶部，带有“首席推荐”或“Top Match”的特殊徽章。
      * 默认展示核心摘要（照片、昵称、分数、一句话亮点）。
      * 用户可点击展开，沉浸式地阅读包含模拟对话在内的**完整报告**。
  * **潜力候选人 (Top 2-5):**
      * 以尺寸较小的、统一的卡片样式，按排名顺序陈列在“首席推荐”下方。
      * 每张卡片清晰地展示该候选人的核心信息（照片、昵称、分数）。
      * 用户可点击任一卡片，弹出一个覆盖层或展开一个区域，查看其**简报内容**。
  * **交互：** 每一张卡片（无论是首席还是潜力）上都有独立的“喜欢”和“跳过”按钮，用户可以对 5 名候选人分别做出决定。

### 5\. 用户流程图修订 (User Flow Diagram Revision)

```mermaid
graph TD
    subgraph "寡佬 AI v2.0 匹配引擎"
        A[用户发起匹配请求] --> B["红娘 Agent 激活<br/>(构建候选人池, 生成用户灵魂画像)"];
        B --> C["ARAG-Soul 框架<br/>对池中所有候选人进行分析、打分、提炼亮点"];
        C --> D["红娘 Agent 进行最终排序<br/>并筛选出 Top 5 候选人"];
        D --> E{"生成报告内容"};
        E -- 排名 Top 1 --> F["生成'首席推荐'完整报告<br/>(含模拟对话、约会计划)"];
        E -- 排名 Top 2-5 --> G["生成4份'潜力候选人'简报<br/>(仅含摘要、分数、亮点)"];
        F & G --> H["打包成'智能候选人矩阵'"];
        H --> I["通知用户查阅全新的匹配结果页"];
    end
```

### 6\. 对其他模块的影响 (Impact on Other Modules)

  * **成功指标 (Metrics):**
      * 原“匹配成功率”需细化。新增\*\*“矩阵接受率 (Matrix Acceptance Rate)”\*\*：至少喜欢矩阵中一个人的请求 / 总请求数。
      * 新增\*\*“首席推荐成功率 (Top Match Success Rate)”\*\*：用户喜欢首席推荐的次数 / 总请求数。
  * **商业化 (Monetization):**
      * 这个功能极大地提升了单次匹配的价值，为未来的**高级订阅服务**（例如：请求更多矩阵、查看所有候选人的完整报告等）奠定了坚实的基础。
  * **用户反馈 (Feedback):**
      * 反馈系统需支持对整个“候选人矩阵”的质量进行打分，也可以支持对单个推荐的准确性进行评价。