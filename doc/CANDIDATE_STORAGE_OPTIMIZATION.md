# 候选人数据存储优化总结

## 🎯 优化概述

将候选人矩阵数据从单一 JSON 字段存储优化为结构化的关系型存储，并创建了专门的候选人详情页面，提升了数据管理效率和用户体验。

## 🔄 主要变更

### 1. 数据库结构优化

#### 之前的存储方式
```sql
-- match_requests 表
CREATE TABLE match_requests (
  id UUID PRIMARY KEY,
  requester_id UUID REFERENCES users(id),
  status TEXT DEFAULT 'processing',
  final_report JSONB, -- 整个矩阵存储在一个字段中
  error_message TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- match_candidates 表（简化版）
CREATE TABLE match_candidates (
  id UUID PRIMARY KEY,
  request_id UUID REFERENCES match_requests(id),
  candidate_id UUID REFERENCES users(id),
  rank INTEGER,
  user_decision TEXT DEFAULT 'pending'
);
```

#### 现在的存储方式
```sql
-- match_requests 表（移除 final_report）
CREATE TABLE match_requests (
  id UUID PRIMARY KEY,
  requester_id UUID REFERENCES users(id),
  status TEXT DEFAULT 'processing',
  error_message TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- match_candidates 表（完整结构化数据）
CREATE TABLE match_candidates (
  id UUID PRIMARY KEY,
  request_id UUID REFERENCES match_requests(id),
  candidate_id UUID REFERENCES users(id),
  rank INTEGER NOT NULL, -- 1 = 首席推荐, 2-5 = 潜力候选人
  
  -- 兼容性分析数据
  compatibility_score INTEGER NOT NULL,
  reasoning TEXT,
  highlights JSONB, -- 关系亮点数组
  challenges JSONB, -- 潜在挑战数组
  personality_summary TEXT,
  
  -- 首席推荐的额外数据 (rank = 1 时才有)
  relationship_insight JSONB, -- 关系洞察
  conversation_simulation JSONB, -- 对话模拟
  date_plan JSONB, -- 约会计划
  
  -- 用户决策
  user_decision TEXT DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 2. 数据存储逻辑优化

#### 更新的 `saveCandidateMatrix` 方法
```typescript
static async saveCandidateMatrix(requestId: string, matrix: CandidateMatrix): Promise<void> {
  // 保存首席推荐（包含完整数据）
  const topCandidateData: NewMatchCandidate = {
    requestId,
    candidateId: matrix.topMatch.candidate.candidateId,
    rank: 1,
    compatibilityScore: matrix.topMatch.candidate.compatibilityScore,
    reasoning: matrix.topMatch.candidate.reasoning,
    highlights: matrix.topMatch.candidate.highlights,
    challenges: matrix.topMatch.candidate.challenges,
    personalitySummary: matrix.topMatch.candidate.personalitySummary,
    // 首席推荐的额外数据
    relationshipInsight: matrix.topMatch.relationshipInsight,
    conversationSimulation: matrix.topMatch.conversationSimulation,
    datePlan: matrix.topMatch.datePlan,
    userDecision: 'pending'
  };

  // 保存潜力候选人（简化数据）
  const potentialCandidatesData: NewMatchCandidate[] = matrix.potentialMatches.map((match, index) => ({
    requestId,
    candidateId: match.candidate.candidateId,
    rank: index + 2, // 排名从2开始
    compatibilityScore: match.candidate.compatibilityScore,
    reasoning: match.candidate.reasoning,
    highlights: match.candidate.highlights,
    challenges: match.candidate.challenges,
    personalitySummary: match.candidate.personalitySummary,
    // 潜力候选人不需要额外数据
    relationshipInsight: null,
    conversationSimulation: null,
    datePlan: null,
    userDecision: 'pending'
  }));
}
```

### 3. API 数据重构

#### 更新的矩阵获取 API
```typescript
// 从 match_candidates 表重构数据
const candidates = await db
  .select()
  .from(matchCandidates)
  .where(eq(matchCandidates.requestId, requestId))
  .orderBy(matchCandidates.rank);

const matrix = {
  topMatch: {
    candidate: {
      candidateId: topMatch.candidateId,
      compatibilityScore: topMatch.compatibilityScore,
      reasoning: topMatch.reasoning,
      highlights: topMatch.highlights as string[],
      challenges: topMatch.challenges as string[],
      personalitySummary: topMatch.personalitySummary,
    },
    relationshipInsight: topMatch.relationshipInsight,
    conversationSimulation: topMatch.conversationSimulation,
    datePlan: topMatch.datePlan,
  },
  potentialMatches: potentialMatches.map(match => ({
    candidate: {
      candidateId: match.candidateId,
      compatibilityScore: match.compatibilityScore,
      reasoning: match.reasoning,
      highlights: match.highlights as string[],
      challenges: match.challenges as string[],
      personalitySummary: match.personalitySummary,
    },
    compatibilityReason: match.reasoning,
  }))
};
```

## 🎨 新的候选人详情页面

### 页面路径
```
/matches/matrix/[requestId]/candidate/[candidateId]
```

### 页面功能

#### 1. 候选人基本信息展示
- 排名标识（首席推荐 vs 潜力候选人）
- 兼容性评分
- 人格摘要
- 关系亮点标签
- 兼容性分析详情

#### 2. 首席推荐的详细内容（Tab 布局）

**关系洞察标签页**：
- 关系优势列表
- 潜在挑战分析
- 相处建议

**模拟对话标签页**：
- 对话场景描述
- 逐步播放对话
- 对话分析总结

**约会计划标签页**：
- 约会主题和描述
- 地点、时长、预算信息
- 活动安排列表
- 推荐理由

#### 3. 交互功能
- 喜欢/跳过决策
- 对话播放控制
- 返回候选人矩阵

### 技术实现

#### 候选人详情 API
```typescript
// GET /api/matches/matrix/[requestId]/candidates/[candidateId]/detail
export async function GET(request: NextRequest, context: { params: Promise<RouteParams> }) {
  // 权限验证
  // 获取候选人详情和基本信息
  const candidateDetail = await db
    .select({
      // match_candidates 表的字段
      id: matchCandidates.id,
      requestId: matchCandidates.requestId,
      // ... 其他字段
      
      // 候选人基本信息
      candidateName: users.name,
      candidateAge: users.age,
      candidateGender: users.gender,
      candidateInterests: users.interests,
      candidateBio: userProfiles.selfDescription,
    })
    .from(matchCandidates)
    .leftJoin(users, eq(matchCandidates.candidateId, users.id))
    .leftJoin(userProfiles, eq(users.id, userProfiles.userId))
    .where(
      and(
        eq(matchCandidates.requestId, requestId),
        eq(matchCandidates.candidateId, candidateId)
      )
    );
}
```

#### 页面组件结构
```typescript
// 主页面组件
export default function CandidateDetailPage() {
  // 状态管理
  // 数据获取
  // 决策处理
  // 对话播放逻辑
}

// 子组件
function RelationshipInsightTab({ insight, challenges }) // 关系洞察
function ConversationTab({ simulation, ... }) // 对话模拟
function DatePlanTab({ datePlan }) // 约会计划
```

## ✅ 优化收益

### 1. 数据管理优势
- **结构化存储**：每个候选人的数据独立存储，便于查询和管理
- **存储效率**：避免大 JSON 字段，减少数据冗余
- **查询性能**：可以针对特定候选人进行精确查询
- **数据完整性**：通过外键约束保证数据一致性

### 2. 功能扩展性
- **个性化展示**：每个候选人可以有独立的详情页面
- **决策跟踪**：可以精确跟踪用户对每个候选人的决策
- **统计分析**：便于进行候选人匹配效果分析
- **批量操作**：支持批量更新候选人状态

### 3. 用户体验提升
- **详细分析**：用户可以深入了解每个候选人
- **交互体验**：对话播放、约会计划等丰富的交互功能
- **导航便利**：清晰的页面结构和导航
- **响应式设计**：适配不同设备的显示

### 4. 开发维护优势
- **代码组织**：页面组件结构清晰，便于维护
- **类型安全**：完整的 TypeScript 类型定义
- **API 设计**：RESTful API 设计，符合最佳实践
- **错误处理**：完善的错误处理和用户反馈

## 🚀 部署准备

### 数据库迁移
```bash
# 生成迁移文件
npm run db:generate

# 应用迁移（生产环境）
npm run db:migrate
```

### 新增的文件
- `src/app/matches/matrix/[requestId]/candidate/[candidateId]/page.tsx` - 候选人详情页面
- `src/app/api/matches/matrix/[requestId]/candidates/[candidateId]/detail/route.ts` - 候选人详情 API
- `drizzle/0002_premium_cobalt_man.sql` - 数据库迁移文件
- `drizzle/0003_classy_wasp.sql` - 数据库迁移文件

### 更新的文件
- `src/lib/db/schema.ts` - 数据库结构优化
- `src/lib/services/matching-v2.ts` - 数据存储逻辑更新
- `src/app/api/matches/matrix/route.ts` - 矩阵获取 API 重构
- `src/components/CandidateMatrix.tsx` - 添加查看详情按钮
- `src/app/matches/matrix/[requestId]/page.tsx` - 传递 requestId 参数

## 🎉 优化完成

现在候选人数据采用了更合理的存储结构，用户可以通过专门的详情页面深入了解每个候选人，系统的可维护性和用户体验都得到了显著提升！🚀
