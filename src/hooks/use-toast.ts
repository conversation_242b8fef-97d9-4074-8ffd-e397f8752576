// Toast hook - 简化版本

import { useState, useCallback } from 'react';

interface ToastProps {
  title: string;
  description?: string;
  variant?: 'default' | 'destructive';
}

export function useToast() {
  const [toasts, setToasts] = useState<ToastProps[]>([]);

  const toast = useCallback((props: ToastProps) => {
    // 简单的 console 输出，实际项目中应该使用真正的 toast 组件
    console.log(`Toast: ${props.title}`, props.description);
    
    // 如果有真正的 toast 组件，在这里调用
    // 这里我们简单地添加到状态中
    setToasts(prev => [...prev, props]);
    
    // 3秒后自动移除
    setTimeout(() => {
      setToasts(prev => prev.slice(1));
    }, 3000);
  }, []);

  return { toast, toasts };
}
