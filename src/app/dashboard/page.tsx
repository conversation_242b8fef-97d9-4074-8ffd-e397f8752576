'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Heart, Brain, Users, Eye, Plus, Star } from 'lucide-react';
import Link from 'next/link';

interface MatchMatrix {
  id: string;
  status: 'processing' | 'completed' | 'failed';
  createdAt: string;
  candidatesCount?: number;
}

interface ReceivedMatch {
  id: string;
  requestId: string;
  rank: number;
  compatibilityScore: number;
  reasoning: string;
  highlights: string[];
  personalitySummary: string;
  userDecision: string;
  createdAt: string;
  requester: {
    id: string;
    name: string;
    age: number;
    gender: string;
    interests: string[];
    bio: string;
  };
}

export default function DashboardPage() {
  const router = useRouter();
  const supabase = createClient();
  const [matchMatrices, setMatchMatrices] = useState<MatchMatrix[]>([]);
  const [receivedMatches, setReceivedMatches] = useState<ReceivedMatch[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('matrices');

  useEffect(() => {
    checkUser();
  }, []);

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      router.push('/auth/login');
      return;
    }
    await fetchDashboardData();
  };

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // 获取用户的匹配矩阵
      const matricesResponse = await fetch('/api/matches/matrix/list');
      if (matricesResponse.ok) {
        const matricesData = await matricesResponse.json();
        setMatchMatrices(matricesData.matrices || []);
      }

      // 获取收到的匹配请求
      const receivedResponse = await fetch('/api/matches/received');
      if (receivedResponse.ok) {
        const receivedData = await receivedResponse.json();
        setReceivedMatches(receivedData.receivedMatches || []);
      }
    } catch (error) {
      console.error('获取仪表板数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateMatrix = async () => {
    try {
      const response = await fetch('/api/matches/matrix', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        router.push(`/matches/matrix/${result.requestId}`);
      } else {
        alert('生成匹配矩阵失败，请稍后重试');
      }
    } catch (error) {
      console.error('创建匹配矩阵失败:', error);
      alert('生成匹配矩阵失败，请稍后重试');
    }
  };

  const handleRespondToMatch = async (matchId: string, decision: 'liked' | 'skipped') => {
    try {
      const response = await fetch('/api/matches/received', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          matchCandidateId: matchId,
          decision
        }),
      });

      if (response.ok) {
        const result = await response.json();
        
        if (result.mutualMatch && result.contactInfo) {
          alert(result.contactInfo.message);
        }
        
        // 刷新数据
        fetchDashboardData();
      }
    } catch (error) {
      console.error('回应匹配失败:', error);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">加载中...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-50">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* 页面标题 */}
        <div className="mb-12 text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full mb-4">
            <Heart className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent mb-3">
            我的匹配中心
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            AI 智能匹配，为您寻找最合适的伴侣。管理您的匹配矩阵，回应收到的喜欢，开启美好的缘分之旅。
          </p>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card className="bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">我的匹配矩阵</p>
                  <p className="text-3xl font-bold text-gray-900">{matchMatrices.length}</p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Brain className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">收到的喜欢</p>
                  <p className="text-3xl font-bold text-gray-900">{receivedMatches.length}</p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg flex items-center justify-center">
                  <Heart className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">互相匹配</p>
                  <p className="text-3xl font-bold text-gray-900">
                    {receivedMatches.filter(m => m.userDecision === 'mutual_liked').length}
                  </p>
                </div>
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                  <Users className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 快速操作 */}
        <div className="mb-8">
          <Card className="bg-gradient-to-r from-pink-500 to-purple-600 border-0 shadow-xl">
            <CardContent className="p-8">
              <div className="flex items-center justify-between">
                <div className="text-white">
                  <h3 className="text-2xl font-bold mb-2">开始新的匹配之旅</h3>
                  <p className="text-pink-100 text-lg">
                    让 AI 为您分析并推荐最合适的候选人，开启专属的缘分体验
                  </p>
                </div>
                <Button
                  onClick={handleCreateMatrix}
                  size="lg"
                  className="bg-white text-pink-600 hover:bg-pink-50 hover:text-pink-700 font-semibold px-8 py-3 text-lg shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Plus className="w-5 h-5 mr-2" />
                  生成匹配矩阵
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

      {/* 主要内容 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 bg-white/70 backdrop-blur-sm border-0 shadow-lg h-14">
          <TabsTrigger
            value="matrices"
            className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-blue-600 data-[state=active]:text-white font-medium text-lg"
          >
            <Brain className="w-5 h-5" />
            我的匹配矩阵 ({matchMatrices.length})
          </TabsTrigger>
          <TabsTrigger
            value="received"
            className="flex items-center gap-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-pink-500 data-[state=active]:to-pink-600 data-[state=active]:text-white font-medium text-lg"
          >
            <Heart className="w-5 h-5" />
            收到的喜欢 ({receivedMatches.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="matrices" className="space-y-4">
          <MatchMatricesTab matrices={matchMatrices} />
        </TabsContent>

        <TabsContent value="received" className="space-y-4">
          <ReceivedMatchesTab 
            matches={receivedMatches} 
            onRespond={handleRespondToMatch}
          />
        </TabsContent>
      </Tabs>
      </div>
    </div>
  );
}

// 匹配矩阵标签页
function MatchMatricesTab({ matrices }: { matrices: MatchMatrix[] }) {
  if (matrices.length === 0) {
    return (
      <Card className="bg-white/70 backdrop-blur-sm border-0 shadow-lg">
        <CardContent className="p-12 text-center">
          <div className="w-20 h-20 bg-gradient-to-r from-blue-100 to-blue-200 rounded-full flex items-center justify-center mx-auto mb-6">
            <Brain className="w-10 h-10 text-blue-600" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-3">开始您的匹配之旅</h3>
          <p className="text-gray-600 text-lg mb-6 max-w-md mx-auto">
            还没有匹配矩阵。点击上方按钮，让 AI 为您生成专属的候选人推荐。
          </p>
          <div className="inline-flex items-center gap-2 text-blue-600 font-medium">
            <Plus className="w-4 h-4" />
            <span>生成您的第一个匹配矩阵</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid gap-6">
      {matrices.map((matrix) => (
        <Card key={matrix.id} className="bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
          <CardContent className="p-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-6">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300">
                  <Brain className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-1">
                    匹配矩阵 #{matrix.id.slice(-8)}
                  </h3>
                  <p className="text-gray-600 mb-2">
                    创建于 {new Date(matrix.createdAt).toLocaleDateString()}
                  </p>
                  {matrix.candidatesCount && (
                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4 text-gray-500" />
                      <span className="text-sm text-gray-500">
                        包含 {matrix.candidatesCount} 个候选人
                      </span>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-4">
                <Badge
                  variant={matrix.status === 'completed' ? 'default' : 'secondary'}
                  className={`px-4 py-2 text-sm font-medium ${
                    matrix.status === 'completed'
                      ? 'bg-green-100 text-green-800 hover:bg-green-200'
                      : matrix.status === 'processing'
                      ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
                      : 'bg-red-100 text-red-800 hover:bg-red-200'
                  }`}
                >
                  {matrix.status === 'completed' ? '✅ 已完成' :
                   matrix.status === 'processing' ? '⏳ 处理中' : '❌ 失败'}
                </Badge>

                {matrix.status === 'completed' && (
                  <Link href={`/matches/matrix/${matrix.id}`}>
                    <Button
                      size="lg"
                      className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      <Eye className="w-4 h-4 mr-2" />
                      查看详情
                    </Button>
                  </Link>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// 收到的匹配标签页
function ReceivedMatchesTab({
  matches,
  onRespond
}: {
  matches: ReceivedMatch[];
  onRespond: (matchId: string, decision: 'liked' | 'skipped') => void;
}) {
  if (matches.length === 0) {
    return (
      <Card className="bg-white/70 backdrop-blur-sm border-0 shadow-lg">
        <CardContent className="p-12 text-center">
          <div className="w-20 h-20 bg-gradient-to-r from-pink-100 to-pink-200 rounded-full flex items-center justify-center mx-auto mb-6">
            <Heart className="w-10 h-10 text-pink-600" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-3">等待缘分降临</h3>
          <p className="text-gray-600 text-lg mb-6 max-w-md mx-auto">
            还没有收到喜欢。当有人对您感兴趣时，会在这里显示。保持耐心，缘分正在路上！
          </p>
          <div className="inline-flex items-center gap-2 text-pink-600 font-medium">
            <Heart className="w-4 h-4" />
            <span>期待第一个喜欢</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid gap-6">
      {matches.map((match) => (
        <Card key={match.id} className="bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 group">
          <CardContent className="p-8">
            <div className="flex items-start justify-between">
              <div className="flex gap-6 flex-1">
                <div className="w-20 h-20 bg-gradient-to-br from-pink-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-105 transition-transform duration-300">
                  <Users className="w-10 h-10 text-white" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-3">
                    <h3 className="text-xl font-bold text-gray-900">
                      {match.requester.name}
                    </h3>
                    <Badge variant="secondary" className="px-3 py-1 text-sm font-medium bg-gray-100 text-gray-700">
                      {match.requester.age}岁
                    </Badge>
                    <Badge variant="outline" className="px-3 py-1 text-sm font-medium border-pink-200 text-pink-700 bg-pink-50">
                      匹配度 {match.compatibilityScore}
                    </Badge>
                    {match.rank === 1 && (
                      <Badge className="bg-gradient-to-r from-pink-500 to-purple-600 text-white px-3 py-1 shadow-md">
                        <Star className="w-3 h-3 mr-1" />
                        首席推荐
                      </Badge>
                    )}
                  </div>

                  <p className="text-gray-700 mb-4 text-lg leading-relaxed">
                    {match.personalitySummary}
                  </p>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {match.highlights.slice(0, 3).map((highlight, index) => (
                      <Badge key={index} variant="secondary" className="text-sm px-3 py-1 bg-purple-100 text-purple-700 border-purple-200">
                        {highlight}
                      </Badge>
                    ))}
                    {match.highlights.length > 3 && (
                      <Badge variant="outline" className="text-sm px-3 py-1 text-gray-500">
                        +{match.highlights.length - 3} 更多
                      </Badge>
                    )}
                  </div>

                  <div className="flex items-center gap-2 text-gray-500">
                    <Heart className="w-4 h-4" />
                    <span className="text-sm">
                      {new Date(match.createdAt).toLocaleDateString()} 喜欢了您
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex flex-col items-end gap-3">
                {match.userDecision === 'liked' && (
                  <div className="flex gap-3">
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={() => onRespond(match.id, 'skipped')}
                      className="px-6 py-3 border-gray-300 hover:border-gray-400 hover:bg-gray-50"
                    >
                      跳过
                    </Button>
                    <Button
                      size="lg"
                      onClick={() => onRespond(match.id, 'liked')}
                      className="bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white px-6 py-3 shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      <Heart className="w-5 h-5 mr-2" />
                      喜欢回应
                    </Button>
                  </div>
                )}

                {match.userDecision === 'mutual_liked' && (
                  <Badge className="bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-2 text-lg shadow-lg">
                    <Heart className="w-4 h-4 mr-2" />
                    🎉 互相喜欢
                  </Badge>
                )}

                {match.userDecision === 'skipped' && (
                  <Badge variant="secondary" className="px-4 py-2 text-lg bg-gray-100 text-gray-600">
                    已跳过
                  </Badge>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
