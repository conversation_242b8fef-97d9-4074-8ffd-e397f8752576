// Cron Job API - 定时处理匹配队列
// 可以通过外部 cron 服务（如 cron-job.org）定期调用

import { NextRequest, NextResponse } from 'next/server';
import { MatchingServiceV2 } from '@/lib/services/matching-v2';

export async function GET(request: NextRequest) {
  try {
    // 验证 cron 密钥（可选）
    const cronSecret = request.nextUrl.searchParams.get('secret');
    const expectedSecret = process.env.CRON_SECRET;
    
    if (expectedSecret && cronSecret !== expectedSecret) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('⏰ Cron: 开始定时处理匹配队列');
    
    // 处理队列中的任务
    const result = await MatchingServiceV2.processQueuedRequests();
    
    const response = {
      success: true,
      message: '定时队列处理完成',
      processedCount: result.processedCount,
      timestamp: new Date().toISOString()
    };

    console.log('✅ Cron: 定时处理完成', response);
    
    return NextResponse.json(response);

  } catch (error) {
    console.error('❌ Cron: 定时处理失败:', error);
    return NextResponse.json({
      error: 'CRON_PROCESSING_FAILED',
      message: error instanceof Error ? error.message : '定时处理失败',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// 支持 POST 方法（某些 cron 服务可能使用 POST）
export async function POST(request: NextRequest) {
  return GET(request);
}
