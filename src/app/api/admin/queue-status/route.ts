// 队列状态监控 API

import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { matchQueue, matchRequests } from '@/lib/db/schema';
import { eq, sql } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    // 简单的管理员验证
    const adminSecret = request.nextUrl.searchParams.get('admin_secret');
    const expectedSecret = process.env.ADMIN_SECRET;
    
    if (expectedSecret && adminSecret !== expectedSecret) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 获取队列统计
    const queueStats = await db
      .select({
        status: matchQueue.status,
        count: sql<number>`count(*)`
      })
      .from(matchQueue)
      .groupBy(matchQueue.status);

    // 获取请求统计
    const requestStats = await db
      .select({
        status: matchRequests.status,
        count: sql<number>`count(*)`
      })
      .from(matchRequests)
      .groupBy(matchRequests.status);

    // 获取最近的队列任务
    const recentTasks = await db
      .select({
        id: matchQueue.id,
        matchRequestId: matchQueue.matchRequestId,
        requesterId: matchQueue.requesterId,
        status: matchQueue.status,
        attempts: matchQueue.attempts,
        createdAt: matchQueue.createdAt
      })
      .from(matchQueue)
      .orderBy(sql`${matchQueue.createdAt} DESC`)
      .limit(10);

    // 获取失败的任务
    const failedTasks = await db
      .select()
      .from(matchQueue)
      .where(eq(matchQueue.status, 'failed'))
      .limit(5);

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      queueStats: queueStats.reduce((acc, stat) => {
        acc[stat.status || 'unknown'] = Number(stat.count);
        return acc;
      }, {} as Record<string, number>),
      requestStats: requestStats.reduce((acc, stat) => {
        acc[stat.status || 'unknown'] = Number(stat.count);
        return acc;
      }, {} as Record<string, number>),
      recentTasks,
      failedTasks: failedTasks.length,
      summary: {
        totalQueueItems: queueStats.reduce((sum, stat) => sum + Number(stat.count), 0),
        totalRequests: requestStats.reduce((sum, stat) => sum + Number(stat.count), 0),
        pendingItems: queueStats.find(s => s.status === 'pending')?.count || 0,
        processingItems: queueStats.find(s => s.status === 'processing')?.count || 0,
        failedItems: queueStats.find(s => s.status === 'failed')?.count || 0
      }
    });

  } catch (error) {
    console.error('获取队列状态失败:', error);
    return NextResponse.json({
      error: 'QUEUE_STATUS_ERROR',
      message: error instanceof Error ? error.message : '获取队列状态失败'
    }, { status: 500 });
  }
}
