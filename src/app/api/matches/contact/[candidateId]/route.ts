// 获取互相匹配的联系信息 API

import { NextRequest, NextResponse } from 'next/server';
import { createRouteClient } from '@/lib/supabase/server';
import { db } from '@/lib/db';
import { matchCandidates, matchRequests, users } from '@/lib/db/schema';
import { eq, and, or } from 'drizzle-orm';

interface RouteParams {
  candidateId: string;
}

// 获取互相匹配的联系信息
export async function GET(
  request: NextRequest,
  context: { params: Promise<RouteParams> }
) {
  try {
    const supabase = createRouteClient();
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const params = await context.params;
    const { candidateId } = params;

    // 验证是否存在互相匹配的记录
    const mutualMatch = await db
      .select({
        id: matchCandidates.id,
        userDecision: matchCandidates.userDecision,
        requesterId: matchRequests.requesterId
      })
      .from(matchCandidates)
      .innerJoin(matchRequests, eq(matchCandidates.requestId, matchRequests.id))
      .where(
        and(
          or(
            // 当前用户是发起者，候选人是目标
            and(
              eq(matchRequests.requesterId, user.id),
              eq(matchCandidates.candidateId, candidateId)
            ),
            // 当前用户是候选人，发起者是目标
            and(
              eq(matchRequests.requesterId, candidateId),
              eq(matchCandidates.candidateId, user.id)
            )
          ),
          eq(matchCandidates.userDecision, 'mutual_liked')
        )
      )
      .limit(1);

    if (mutualMatch.length === 0) {
      return NextResponse.json({ 
        error: 'NOT_MUTUAL_MATCH',
        message: '只有互相喜欢的用户才能查看联系信息' 
      }, { status: 403 });
    }

    // 获取双方的联系信息
    const contactUsers = await db
      .select({
        id: users.id,
        name: users.name,
        email: users.email
      })
      .from(users)
      .where(
        or(
          eq(users.id, user.id),
          eq(users.id, candidateId)
        )
      );

    const contactInfo = {
      users: contactUsers,
      message: '恭喜！你们互相喜欢了！现在可以开始联系了。',
      matchedAt: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      contactInfo
    });

  } catch (error) {
    console.error('获取联系信息失败:', error);
    return NextResponse.json({
      error: 'INTERNAL_SERVER_ERROR',
      message: error instanceof Error ? error.message : '服务器内部错误'
    }, { status: 500 });
  }
}
