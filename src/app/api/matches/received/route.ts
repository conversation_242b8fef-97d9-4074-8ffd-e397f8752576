// 获取候选人收到的匹配请求 API

import { NextRequest, NextResponse } from 'next/server';
import { createRouteClient } from '@/lib/supabase/server';
import { db } from '@/lib/db';
import { matchCandidates, matchRequests, users, userProfiles } from '@/lib/db/schema';
import { eq, and, or } from 'drizzle-orm';

// 获取当前用户收到的匹配请求
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteClient();
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 获取当前用户作为候选人收到的所有喜欢
    const receivedMatches = await db
      .select({
        // 匹配候选人信息
        id: matchCandidates.id,
        requestId: matchCandidates.requestId,
        rank: matchCandidates.rank,
        compatibilityScore: matchCandidates.compatibilityScore,
        reasoning: matchCandidates.reasoning,
        highlights: matchCandidates.highlights,
        challenges: matchCandidates.challenges,
        personalitySummary: matchCandidates.personalitySummary,
        userDecision: matchCandidates.userDecision,
        createdAt: matchCandidates.createdAt,
        
        // 发起者信息
        requesterId: matchRequests.requesterId,
        requesterName: users.name,
        requesterAge: users.age,
        requesterGender: users.gender,
        requesterInterests: users.interests,
        requesterBio: userProfiles.selfDescription,
      })
      .from(matchCandidates)
      .innerJoin(matchRequests, eq(matchCandidates.requestId, matchRequests.id))
      .leftJoin(users, eq(matchRequests.requesterId, users.id))
      .leftJoin(userProfiles, eq(users.id, userProfiles.userId))
      .where(
        and(
          eq(matchCandidates.candidateId, user.id), // 当前用户作为候选人
          eq(matchCandidates.userDecision, 'liked') // 对方已经喜欢了
        )
      )
      .orderBy(matchCandidates.createdAt);

    // 格式化返回数据
    const formattedMatches = receivedMatches.map(match => ({
      id: match.id,
      requestId: match.requestId,
      rank: match.rank,
      compatibilityScore: match.compatibilityScore,
      reasoning: match.reasoning,
      highlights: match.highlights as string[],
      challenges: match.challenges as string[],
      personalitySummary: match.personalitySummary,
      userDecision: match.userDecision,
      createdAt: match.createdAt,
      requester: {
        id: match.requesterId,
        name: match.requesterName,
        age: match.requesterAge,
        gender: match.requesterGender,
        interests: match.requesterInterests as string[],
        bio: match.requesterBio,
      }
    }));

    return NextResponse.json({
      success: true,
      receivedMatches: formattedMatches,
      count: formattedMatches.length
    });

  } catch (error) {
    console.error('获取收到的匹配请求失败:', error);
    return NextResponse.json({
      error: 'INTERNAL_SERVER_ERROR',
      message: error instanceof Error ? error.message : '服务器内部错误'
    }, { status: 500 });
  }
}

// 候选人回应匹配请求
export async function PATCH(request: NextRequest) {
  try {
    const supabase = createRouteClient();
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { matchCandidateId, decision } = await request.json();

    if (!matchCandidateId || !decision || !['liked', 'skipped'].includes(decision)) {
      return NextResponse.json({ 
        error: 'INVALID_REQUEST',
        message: '请求参数无效' 
      }, { status: 400 });
    }

    // 验证这个匹配候选人记录确实属于当前用户
    const matchCandidate = await db
      .select()
      .from(matchCandidates)
      .where(
        and(
          eq(matchCandidates.id, matchCandidateId),
          eq(matchCandidates.candidateId, user.id),
          eq(matchCandidates.userDecision, 'liked') // 确保对方已经喜欢了
        )
      )
      .limit(1);

    if (matchCandidate.length === 0) {
      return NextResponse.json({ 
        error: 'NOT_FOUND',
        message: '匹配记录不存在或无权限操作' 
      }, { status: 404 });
    }

    let mutualMatch = false;
    let contactInfo = null;

    if (decision === 'liked') {
      // 候选人也喜欢，创建互相匹配
      
      // 获取发起者信息
      const matchRequest = await db
        .select()
        .from(matchRequests)
        .where(eq(matchRequests.id, matchCandidate[0].requestId))
        .limit(1);

      if (matchRequest.length > 0) {
        const requesterId = matchRequest[0].requesterId;
        
        // 更新互相匹配状态
        await db
          .update(matchCandidates)
          .set({ userDecision: 'mutual_liked' })
          .where(eq(matchCandidates.id, matchCandidateId));

        // 获取联系信息
        const userList = await db
          .select({
            id: users.id,
            name: users.name,
            email: users.email
          })
          .from(users)
          .where(
            or(
              eq(users.id, user.id),
              eq(users.id, requesterId)
            )
          );

        mutualMatch = true;
        contactInfo = {
          users: userList,
          message: '恭喜！你们互相喜欢了！现在可以开始联系了。'
        };
      }
    } else {
      // 候选人跳过，更新状态为 skipped
      await db
        .update(matchCandidates)
        .set({ userDecision: 'skipped' })
        .where(eq(matchCandidates.id, matchCandidateId));
    }

    return NextResponse.json({
      success: true,
      mutualMatch,
      contactInfo,
      message: decision === 'liked' ? 
        (mutualMatch ? '恭喜！你们互相喜欢了！' : '已表达喜欢') : 
        '已跳过此匹配'
    });

  } catch (error) {
    console.error('回应匹配请求失败:', error);
    return NextResponse.json({
      error: 'INTERNAL_SERVER_ERROR',
      message: error instanceof Error ? error.message : '服务器内部错误'
    }, { status: 500 });
  }
}
