// 获取用户的匹配矩阵列表 API

import { NextRequest, NextResponse } from 'next/server';
import { createRouteClient } from '@/lib/supabase/server';
import { db } from '@/lib/db';
import { matchRequests, matchCandidates } from '@/lib/db/schema';
import { eq, sql } from 'drizzle-orm';

// 获取用户的匹配矩阵列表
export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteClient();
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // 获取用户的所有匹配请求，并统计候选人数量
    const matrices = await db
      .select({
        id: matchRequests.id,
        status: matchRequests.status,
        createdAt: matchRequests.createdAt,
        candidatesCount: sql<number>`count(${matchCandidates.id})`
      })
      .from(matchRequests)
      .leftJoin(matchCandidates, eq(matchRequests.id, matchCandidates.requestId))
      .where(eq(matchRequests.requesterId, user.id))
      .groupBy(matchRequests.id, matchRequests.status, matchRequests.createdAt)
      .orderBy(sql`${matchRequests.createdAt} DESC`);

    // 格式化返回数据
    const formattedMatrices = matrices.map(matrix => ({
      id: matrix.id,
      status: matrix.status,
      createdAt: matrix.createdAt?.toISOString(),
      candidatesCount: Number(matrix.candidatesCount) || 0
    }));

    return NextResponse.json({
      success: true,
      matrices: formattedMatrices,
      count: formattedMatrices.length
    });

  } catch (error) {
    console.error('获取匹配矩阵列表失败:', error);
    return NextResponse.json({
      error: 'INTERNAL_SERVER_ERROR',
      message: error instanceof Error ? error.message : '服务器内部错误'
    }, { status: 500 });
  }
}
