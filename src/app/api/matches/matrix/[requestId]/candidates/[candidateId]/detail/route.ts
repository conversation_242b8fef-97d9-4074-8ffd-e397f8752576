// 候选人详情 API

import { NextRequest, NextResponse } from 'next/server';
import { createRouteClient } from '@/lib/supabase/server';
import { db } from '@/lib/db';
import { matchCandidates, matchRequests, users, userProfiles } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

interface RouteParams {
  requestId: string;
  candidateId: string;
}

// 获取候选人详情
export async function GET(
  request: NextRequest,
  context: { params: Promise<RouteParams> }
) {
  try {
    const supabase = createRouteClient();
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const params = await context.params;
    const { requestId, candidateId } = params;

    // 验证请求权限
    const matchRequest = await db
      .select()
      .from(matchRequests)
      .where(eq(matchRequests.id, requestId))
      .limit(1);

    if (matchRequest.length === 0 || matchRequest[0].requesterId !== user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // 获取候选人详情
    const candidateDetail = await db
      .select({
        // match_candidates 表的字段
        id: matchCandidates.id,
        requestId: matchCandidates.requestId,
        candidateId: matchCandidates.candidateId,
        rank: matchCandidates.rank,
        compatibilityScore: matchCandidates.compatibilityScore,
        reasoning: matchCandidates.reasoning,
        highlights: matchCandidates.highlights,
        challenges: matchCandidates.challenges,
        personalitySummary: matchCandidates.personalitySummary,
        relationshipInsight: matchCandidates.relationshipInsight,
        conversationSimulation: matchCandidates.conversationSimulation,
        datePlan: matchCandidates.datePlan,
        userDecision: matchCandidates.userDecision,
        createdAt: matchCandidates.createdAt,
        
        // 候选人基本信息
        candidateName: users.name,
        candidateAge: users.age,
        candidateGender: users.gender,
        candidateInterests: users.interests,
        candidateBio: userProfiles.selfDescription,
      })
      .from(matchCandidates)
      .leftJoin(users, eq(matchCandidates.candidateId, users.id))
      .leftJoin(userProfiles, eq(users.id, userProfiles.userId))
      .where(
        and(
          eq(matchCandidates.requestId, requestId),
          eq(matchCandidates.candidateId, candidateId)
        )
      )
      .limit(1);

    if (candidateDetail.length === 0) {
      return NextResponse.json({ error: 'Candidate not found' }, { status: 404 });
    }

    const candidate = candidateDetail[0];

    // 构造返回数据
    const responseData = {
      id: candidate.id,
      requestId: candidate.requestId,
      candidateId: candidate.candidateId,
      rank: candidate.rank,
      compatibilityScore: candidate.compatibilityScore,
      reasoning: candidate.reasoning,
      highlights: candidate.highlights as string[],
      challenges: candidate.challenges as string[],
      personalitySummary: candidate.personalitySummary,
      relationshipInsight: candidate.relationshipInsight,
      conversationSimulation: candidate.conversationSimulation,
      datePlan: candidate.datePlan,
      userDecision: candidate.userDecision,
      candidateInfo: {
        name: candidate.candidateName,
        age: candidate.candidateAge,
        gender: candidate.candidateGender,
        interests: candidate.candidateInterests as string[],
        bio: candidate.candidateBio,
      },
      createdAt: candidate.createdAt,
    };

    return NextResponse.json({
      success: true,
      candidate: responseData
    });

  } catch (error) {
    console.error('获取候选人详情失败:', error);
    return NextResponse.json({
      error: 'INTERNAL_SERVER_ERROR',
      message: error instanceof Error ? error.message : '服务器内部错误'
    }, { status: 500 });
  }
}
