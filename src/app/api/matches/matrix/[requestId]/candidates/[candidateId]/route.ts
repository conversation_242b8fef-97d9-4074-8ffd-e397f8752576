// 候选人决策 API

import { NextRequest, NextResponse } from 'next/server';
import { createRouteClient } from '@/lib/supabase/server';
import { MatchingServiceV2 } from '@/lib/services/matching-v2';

interface RouteParams {
  requestId: string;
  candidateId: string;
}

// 更新候选人决策
export async function PATCH(
  request: NextRequest,
  context: { params: Promise<RouteParams> }
) {
  try {
    const supabase = createRouteClient();
    
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const params = await context.params;
    const { requestId, candidateId } = params;
    const { decision } = await request.json();

    if (!decision || !['liked', 'skipped'].includes(decision)) {
      return NextResponse.json({
        error: 'BAD_REQUEST',
        message: 'decision 必须是 "liked" 或 "skipped"'
      }, { status: 400 });
    }

    const result = await MatchingServiceV2.updateCandidateDecision(
      requestId,
      candidateId,
      user.id,
      decision
    );

    return NextResponse.json({
      success: true,
      decision,
      mutualMatch: result.mutualMatch || false,
      message: result.mutualMatch ? '恭喜！你们互相喜欢了！' : '决策已保存'
    });

  } catch (error) {
    console.error('更新候选人决策失败:', error);
    return NextResponse.json({
      error: 'INTERNAL_SERVER_ERROR',
      message: error instanceof Error ? error.message : '服务器内部错误'
    }, { status: 500 });
  }
}
