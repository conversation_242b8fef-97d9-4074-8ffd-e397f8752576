// 队列处理 Worker API

import { NextRequest, NextResponse } from 'next/server';
import { MatchingServiceV2 } from '@/lib/services/matching-v2';

// 处理匹配队列（手动触发或定时调用）
export async function POST(request: NextRequest) {
  try {
    // 可选的身份验证（用于外部调用）
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.WORKER_SECRET_TOKEN;

    // 如果设置了 token，则验证；否则允许内部调用
    if (expectedToken && authHeader && authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('🔄 Worker: 开始处理匹配队列');

    // 处理队列中的任务
    const result = await MatchingServiceV2.processQueuedRequests();

    return NextResponse.json({
      success: true,
      message: '队列处理完成',
      processedCount: result.processedCount,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Worker: 队列处理失败:', error);
    return NextResponse.json({
      error: 'PROCESSING_FAILED',
      message: error instanceof Error ? error.message : '队列处理失败'
    }, { status: 500 });
  }
}

// 触发队列处理（内部调用）
export async function triggerQueueProcessing() {
  try {
    console.log('🔄 内部触发队列处理');
    await MatchingServiceV2.processQueuedRequests();
    console.log('✅ 内部队列处理完成');
  } catch (error) {
    console.error('❌ 内部队列处理失败:', error);
  }
}

// 健康检查
export async function GET() {
  return NextResponse.json({
    status: 'healthy',
    service: 'queue-processor',
    timestamp: new Date().toISOString()
  });
}
