// 环境变量加载工具

let envLoaded = false;

export function ensureEnvLoaded() {
  if (envLoaded || typeof window !== 'undefined') {
    return;
  }

  try {
    // 尝试加载 .env 文件
    require('dotenv').config({ path: '.env' });
    envLoaded = true;
    console.log('✅ 环境变量已加载');
  } catch (error) {
    // dotenv 可能不存在，尝试其他路径
    try {
      require('dotenv').config({ path: '.env.local' });
      envLoaded = true;
      console.log('✅ 环境变量已从 .env.local 加载');
    } catch (error2) {
      console.warn('⚠️ 无法加载 dotenv，使用系统环境变量');
    }
  }
}

export function getRequiredEnv(key: string): string {
  ensureEnvLoaded();
  
  const value = process.env[key];
  if (!value) {
    throw new Error(`环境变量 ${key} 未设置`);
  }
  return value;
}

export function getOptionalEnv(key: string, defaultValue?: string): string | undefined {
  ensureEnvLoaded();
  
  return process.env[key] || defaultValue;
}

// 验证所有必需的环境变量
export function validateRequiredEnvVars() {
  ensureEnvLoaded();
  
  const required = [
    'DATABASE_URL',
    'OPENROUTER_API_KEY',
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY'
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`缺少必需的环境变量: ${missing.join(', ')}`);
  }
  
  console.log('✅ 所有必需的环境变量都已设置');
}
