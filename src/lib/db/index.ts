import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';
import { ensureEnvLoaded, getOptionalEnv } from '../utils/env';

// 确保环境变量被加载
ensureEnvLoaded();

// Create the connection
const connectionString = getOptionalEnv('DATABASE_URL');

let db: ReturnType<typeof drizzle>;

if (connectionString) {
  // Create postgres client with better configuration for Supabase
  const client = postgres(connectionString, {
    max: 1,
    idle_timeout: 20,
    connect_timeout: 10,
    ssl: 'require',
    prepare: false,
  });

  // Create drizzle instance
  db = drizzle(client, { schema });
} else {
  // Mock database for build time - create a minimal mock
  const mockClient = {} as any;
  db = drizzle(mockClient, { schema });
}

export { db };

// Export schema for use in other files
export * from './schema';
