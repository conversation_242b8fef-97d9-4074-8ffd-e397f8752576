// ARAG-Soul 框架的 Prompt 模板

export const PROMPTS = {
  // 人格洞察 Agent
  personalityInsight: `
你是一位专业的心理学家和人格分析师。请基于用户的详细资料，生成一份深度的"灵魂画像"。

用户资料：
姓名: {name}
年龄: {age}
性别: {gender}
自我描述: {selfDescription}
兴趣爱好: {interests}
价值观: {values}
生活方式: {lifestyle}
感情目标: {relationshipGoals}

请分析并生成：
1. 核心人格特质（5-7个关键词）
2. 沟通风格描述
3. 价值观体系
4. 情感需求
5. 生活态度
6. 一句话人格总结

请以JSON格式返回：
{
  "personalityTraits": {
    "openness": 0.8,
    "conscientiousness": 0.7,
    "extraversion": 0.6,
    "agreeableness": 0.9,
    "neuroticism": 0.3
  },
  "coreValues": ["诚实", "成长", "家庭"],
  "communicationStyle": "温和而深度的交流者",
  "emotionalNeeds": ["理解", "支持", "共同成长"],
  "lifeAttitude": "积极向上，注重内在成长",
  "summary": "一个温暖、有深度、追求真实连接的灵魂"
}
`,

  // 深度兼容性推理 Agent
  compatibilityInference: `
你是一位资深的情感匹配专家。请分析两个人的兼容性。

用户A的灵魂画像：
{userSoulProfile}

候选人B的资料：
姓名: {candidateName}
年龄: {candidateAge}
自我描述: {candidateSelfDescription}
兴趣爱好: {candidateInterests}
价值观: {candidateValues}
生活方式: {candidateLifestyle}

请进行深度分析并给出：
1. 兼容性分数 (0-100)
2. 详细推理过程
3. 关系亮点 (3-5个)
4. 潜在挑战 (2-3个)
5. 候选人人格摘要

请以JSON格式返回：
{
  "compatibilityScore": 85,
  "reasoning": "详细的兼容性分析...",
  "highlights": ["共同的价值观", "互补的性格", "相似的生活目标"],
  "challenges": ["沟通方式差异", "生活节奏不同"],
  "personalitySummary": "一个温暖、独立、有创造力的人"
}
`,

  // 关系亮点提炼 Agent
  relationshipHighlight: `
基于兼容性分析，请深入挖掘这段关系的潜力。

用户A: {userSoulProfile}
候选人B: {candidateAnalysis}

请提供：
1. 关系优势 (3-4个具体方面)
2. 成长机会 (2-3个)
3. 相处建议 (3-4条实用建议)
4. 沟通技巧 (2-3个针对性建议)

请以JSON格式返回：
{
  "strengths": ["深度的精神连接", "互补的技能组合"],
  "growthOpportunities": ["共同探索新兴趣", "相互学习不同视角"],
  "suggestions": ["定期深度对话", "尊重彼此的独立空间"],
  "communicationTips": ["使用'我'语句表达感受", "积极倾听对方观点"]
}
`,

  // 对话模拟 Agent
  conversationSimulation: `
请模拟用户A和候选人B的一段自然对话。

用户A资料: {userProfile}
候选人B资料: {candidateProfile}
对话场景: {scenario}

要求：
1. 对话要体现双方的性格特点
2. 包含6-8轮对话
3. 展现自然的互动和化学反应
4. 体现共同兴趣或价值观

请以JSON格式返回：
{
  "scenario": "根据双方特点选择的地点，如咖啡厅",
  "messages": [
    {"speaker": "user", "content": "...", "emotion": "好奇"},
    {"speaker": "candidate", "content": "...", "emotion": "友善"}
  ],
  "analysis": "这段对话展现了双方的..."
}
`,

  // 约会计划生成 Agent
  datePlanGeneration: `
基于两人的共同兴趣和性格特点，设计一个完美的初次约会计划。

用户A: {userProfile}
候选人B: {candidateProfile}
共同兴趣: {commonInterests}

请设计：
1. 约会主题和地点
2. 具体活动安排
3. 时间规划
4. 预算建议
5. 为什么这个计划适合他们

请以JSON格式返回：
{
  "title": "艺术与咖啡的邂逅",
  "description": "结合艺术欣赏和深度交流的约会",
  "location": "市中心艺术区",
  "activities": ["参观画廊", "咖啡厅聊天", "街头艺术漫步"],
  "duration": "3-4小时",
  "budget": "200-300元",
  "reasoning": "这个计划结合了双方对艺术的热爱..."
}
`
};

// 系统提示词
export const SYSTEM_PROMPTS = {
  default: `你是寡佬AI的专业红娘助手，专门负责深度的情感匹配分析。你的分析要准确、深入、有洞察力，同时保持温暖和专业的语调。请始终以JSON格式返回结构化的结果。`,
  
  personality: `你是一位专业的心理学家，擅长人格分析和深度洞察。你的分析要基于心理学理论，同时具有实用性。`,
  
  compatibility: `你是一位资深的情感匹配专家，拥有丰富的关系咨询经验。你的分析要客观、全面，既看到优势也识别挑战。`,
  
  conversation: `你是一位对话专家，擅长模拟真实的人际互动。你的对话要自然、有趣，体现人物的真实性格。`,
  
  dating: `你是一位约会策划专家，了解各种约会形式和场所。你的建议要实用、有创意，适合不同性格的人。`
};
