/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/matches/matrix/route";
exports.ids = ["app/api/matches/matrix/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmatches%2Fmatrix%2Froute&page=%2Fapi%2Fmatches%2Fmatrix%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmatches%2Fmatrix%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmatches%2Fmatrix%2Froute&page=%2Fapi%2Fmatches%2Fmatrix%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmatches%2Fmatrix%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_ubt22_workspace_indie_lingxiai_gemini_src_app_api_matches_matrix_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/matches/matrix/route.ts */ \"(rsc)/./src/app/api/matches/matrix/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/matches/matrix/route\",\n        pathname: \"/api/matches/matrix\",\n        filename: \"route\",\n        bundlePath: \"app/api/matches/matrix/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/workspace/indie/lingxiai-gemini/src/app/api/matches/matrix/route.ts\",\n    nextConfigOutput,\n    userland: _home_ubt22_workspace_indie_lingxiai_gemini_src_app_api_matches_matrix_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmatches%2Fmatrix%2Froute&page=%2Fapi%2Fmatches%2Fmatrix%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmatches%2Fmatrix%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/matches/matrix/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/matches/matrix/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/uuid/dist/esm/v4.js\");\n/* harmony import */ var _lib_services_matching_v2__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/matching-v2 */ \"(rsc)/./src/lib/services/matching-v2.ts\");\n// V2.0 智能候选人矩阵 API\n\n\n\n\n\n\n\n// 异步处理匹配请求\nasync function processMatchRequestAsync(requestId, requesterId) {\n    try {\n        console.log(`🚀 开始异步处理匹配请求: ${requestId}`);\n        await _lib_services_matching_v2__WEBPACK_IMPORTED_MODULE_4__.MatchingServiceV2.processSingleRequest(requestId, requesterId);\n        console.log(`✅ 异步处理完成: ${requestId}`);\n    } catch (error) {\n        console.error(`❌ 异步处理失败: ${requestId}`, error);\n    }\n}\n// 生成智能候选人矩阵请求\nasync function POST(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createRouteClient)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // 检查每日限制（复用 v1.0 的限制逻辑）\n        const { MatchingService } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_services_matching_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/services/matching */ \"(rsc)/./src/lib/services/matching.ts\"));\n        const limitCheck = await MatchingService.checkDailyMatchLimit(user.id);\n        if (!limitCheck.canMatch) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'DAILY_LIMIT_EXCEEDED',\n                message: '今日匹配次数已用完',\n                limitInfo: limitCheck\n            }, {\n                status: 429\n            });\n        }\n        // 生成请求ID\n        const requestId = (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n        // 创建匹配请求记录\n        const matchRequestData = {\n            id: requestId,\n            requesterId: user.id,\n            status: 'processing'\n        };\n        const [matchRequest] = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchRequests).values(matchRequestData).returning();\n        // 创建任务队列记录\n        const queueData = {\n            matchRequestId: requestId,\n            requesterId: user.id,\n            status: 'pending'\n        };\n        await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchQueue).values(queueData);\n        console.log(`📝 创建匹配矩阵请求: ${requestId} for user: ${user.id}`);\n        // 立即触发异步处理\n        processMatchRequestAsync(requestId, user.id).catch((error)=>{\n            console.error(`异步处理失败 ${requestId}:`, error);\n        });\n        // 立即返回请求ID，实际处理将异步进行\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            requestId: requestId,\n            status: 'processing',\n            message: '专属红娘正在为您筛选候选人，请稍候...',\n            estimatedTime: '2-3分钟'\n        }, {\n            status: 202\n        });\n    } catch (error) {\n        console.error('创建匹配矩阵请求失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'INTERNAL_SERVER_ERROR',\n            message: '服务器内部错误'\n        }, {\n            status: 500\n        });\n    }\n}\n// 获取匹配矩阵结果\nasync function GET(request) {\n    try {\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createRouteClient)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const url = new URL(request.url);\n        const requestId = url.searchParams.get('requestId');\n        if (!requestId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'BAD_REQUEST',\n                message: '缺少 requestId 参数'\n            }, {\n                status: 400\n            });\n        }\n        // 查询匹配请求状态\n        const matchRequest = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchRequests).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchRequests.id, requestId)).limit(1);\n        if (matchRequest.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'NOT_FOUND',\n                message: '匹配请求不存在'\n            }, {\n                status: 404\n            });\n        }\n        const request_data = matchRequest[0];\n        // 检查权限\n        if (request_data.requesterId !== user.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'FORBIDDEN',\n                message: '无权访问此匹配请求'\n            }, {\n                status: 403\n            });\n        }\n        // 根据状态返回不同响应\n        switch(request_data.status){\n            case 'processing':\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    status: 'processing',\n                    message: '正在生成匹配矩阵，请稍候...',\n                    requestId: requestId\n                });\n            case 'completed':\n                // 从 match_candidates 表获取候选人数据\n                const candidates = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchCandidates).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchCandidates.requestId, requestId)).orderBy(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.matchCandidates.rank);\n                if (candidates.length === 0) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'DATA_ERROR',\n                        message: '候选人数据不存在，请重新生成'\n                    }, {\n                        status: 500\n                    });\n                }\n                // 重构候选人矩阵数据\n                const topMatch = candidates.find((c)=>c.rank === 1);\n                const potentialMatches = candidates.filter((c)=>c.rank > 1);\n                if (!topMatch) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'DATA_ERROR',\n                        message: '首席推荐数据不存在，请重新生成'\n                    }, {\n                        status: 500\n                    });\n                }\n                const matrix = {\n                    topMatch: {\n                        candidate: {\n                            candidateId: topMatch.candidateId,\n                            compatibilityScore: topMatch.compatibilityScore,\n                            reasoning: topMatch.reasoning,\n                            highlights: topMatch.highlights,\n                            challenges: topMatch.challenges,\n                            personalitySummary: topMatch.personalitySummary\n                        },\n                        relationshipInsight: topMatch.relationshipInsight,\n                        conversationSimulation: topMatch.conversationSimulation,\n                        datePlan: topMatch.datePlan\n                    },\n                    potentialMatches: potentialMatches.map((match)=>({\n                            candidate: {\n                                candidateId: match.candidateId,\n                                compatibilityScore: match.compatibilityScore,\n                                reasoning: match.reasoning,\n                                highlights: match.highlights,\n                                challenges: match.challenges,\n                                personalitySummary: match.personalitySummary\n                            },\n                            compatibilityReason: match.reasoning\n                        }))\n                };\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    status: 'completed',\n                    requestId: requestId,\n                    matrix: matrix,\n                    generatedAt: request_data.createdAt\n                });\n            case 'failed':\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    status: 'failed',\n                    message: request_data.errorMessage || '匹配生成失败',\n                    requestId: requestId\n                }, {\n                    status: 500\n                });\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'UNKNOWN_STATUS',\n                    message: '未知的请求状态'\n                }, {\n                    status: 500\n                });\n        }\n    } catch (error) {\n        console.error('获取匹配矩阵结果失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'INTERNAL_SERVER_ERROR',\n            message: '服务器内部错误'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/matches/matrix/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiAgentFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiAgentFeedback),\n/* harmony export */   conversations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.conversations),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   matchCandidates: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.matchCandidates),\n/* harmony export */   matchQueue: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.matchQueue),\n/* harmony export */   matchRequests: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.matchRequests),\n/* harmony export */   matches: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.matches),\n/* harmony export */   messages: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.messages),\n/* harmony export */   userProfiles: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.userProfiles),\n/* harmony export */   userSessions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.userSessions),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"(rsc)/./node_modules/postgres/src/index.js\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var _utils_env__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/env */ \"(rsc)/./src/lib/utils/env.ts\");\n\n\n\n\n// 确保环境变量被加载\n(0,_utils_env__WEBPACK_IMPORTED_MODULE_2__.ensureEnvLoaded)();\n// Create the connection\nconst connectionString = (0,_utils_env__WEBPACK_IMPORTED_MODULE_2__.getOptionalEnv)('DATABASE_URL');\nlet db;\nif (connectionString) {\n    // Create postgres client with better configuration for Supabase\n    const client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n        max: 1,\n        idle_timeout: 20,\n        connect_timeout: 10,\n        ssl: 'require',\n        prepare: false\n    });\n    // Create drizzle instance\n    db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_3__.drizzle)(client, {\n        schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n    });\n} else {\n    // Mock database for build time - create a minimal mock\n    const mockClient = {};\n    db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_3__.drizzle)(mockClient, {\n        schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n    });\n}\n\n// Export schema for use in other files\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFrRDtBQUNsQjtBQUNHO0FBQzRCO0FBRS9ELFlBQVk7QUFDWkcsMkRBQWVBO0FBRWYsd0JBQXdCO0FBQ3hCLE1BQU1FLG1CQUFtQkQsMERBQWNBLENBQUM7QUFFeEMsSUFBSUU7QUFFSixJQUFJRCxrQkFBa0I7SUFDcEIsZ0VBQWdFO0lBQ2hFLE1BQU1FLFNBQVNOLG9EQUFRQSxDQUFDSSxrQkFBa0I7UUFDeENHLEtBQUs7UUFDTEMsY0FBYztRQUNkQyxpQkFBaUI7UUFDakJDLEtBQUs7UUFDTEMsU0FBUztJQUNYO0lBRUEsMEJBQTBCO0lBQzFCTixLQUFLTixnRUFBT0EsQ0FBQ08sUUFBUTtRQUFFTCxNQUFNQSxzQ0FBQUE7SUFBQztBQUNoQyxPQUFPO0lBQ0wsdURBQXVEO0lBQ3ZELE1BQU1XLGFBQWEsQ0FBQztJQUNwQlAsS0FBS04sZ0VBQU9BLENBQUNhLFlBQVk7UUFBRVgsTUFBTUEsc0NBQUFBO0lBQUM7QUFDcEM7QUFFYztBQUVkLHVDQUF1QztBQUNkIiwic291cmNlcyI6WyIvaG9tZS91YnQyMi93b3Jrc3BhY2UvaW5kaWUvbGluZ3hpYWktZ2VtaW5pL3NyYy9saWIvZGIvaW5kZXgudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZHJpenpsZSB9IGZyb20gJ2RyaXp6bGUtb3JtL3Bvc3RncmVzLWpzJztcbmltcG9ydCBwb3N0Z3JlcyBmcm9tICdwb3N0Z3Jlcyc7XG5pbXBvcnQgKiBhcyBzY2hlbWEgZnJvbSAnLi9zY2hlbWEnO1xuaW1wb3J0IHsgZW5zdXJlRW52TG9hZGVkLCBnZXRPcHRpb25hbEVudiB9IGZyb20gJy4uL3V0aWxzL2Vudic7XG5cbi8vIOehruS/neeOr+Wig+WPmOmHj+iiq+WKoOi9vVxuZW5zdXJlRW52TG9hZGVkKCk7XG5cbi8vIENyZWF0ZSB0aGUgY29ubmVjdGlvblxuY29uc3QgY29ubmVjdGlvblN0cmluZyA9IGdldE9wdGlvbmFsRW52KCdEQVRBQkFTRV9VUkwnKTtcblxubGV0IGRiOiBSZXR1cm5UeXBlPHR5cGVvZiBkcml6emxlPjtcblxuaWYgKGNvbm5lY3Rpb25TdHJpbmcpIHtcbiAgLy8gQ3JlYXRlIHBvc3RncmVzIGNsaWVudCB3aXRoIGJldHRlciBjb25maWd1cmF0aW9uIGZvciBTdXBhYmFzZVxuICBjb25zdCBjbGllbnQgPSBwb3N0Z3Jlcyhjb25uZWN0aW9uU3RyaW5nLCB7XG4gICAgbWF4OiAxLFxuICAgIGlkbGVfdGltZW91dDogMjAsXG4gICAgY29ubmVjdF90aW1lb3V0OiAxMCxcbiAgICBzc2w6ICdyZXF1aXJlJyxcbiAgICBwcmVwYXJlOiBmYWxzZSxcbiAgfSk7XG5cbiAgLy8gQ3JlYXRlIGRyaXp6bGUgaW5zdGFuY2VcbiAgZGIgPSBkcml6emxlKGNsaWVudCwgeyBzY2hlbWEgfSk7XG59IGVsc2Uge1xuICAvLyBNb2NrIGRhdGFiYXNlIGZvciBidWlsZCB0aW1lIC0gY3JlYXRlIGEgbWluaW1hbCBtb2NrXG4gIGNvbnN0IG1vY2tDbGllbnQgPSB7fSBhcyBhbnk7XG4gIGRiID0gZHJpenpsZShtb2NrQ2xpZW50LCB7IHNjaGVtYSB9KTtcbn1cblxuZXhwb3J0IHsgZGIgfTtcblxuLy8gRXhwb3J0IHNjaGVtYSBmb3IgdXNlIGluIG90aGVyIGZpbGVzXG5leHBvcnQgKiBmcm9tICcuL3NjaGVtYSc7XG4iXSwibmFtZXMiOlsiZHJpenpsZSIsInBvc3RncmVzIiwic2NoZW1hIiwiZW5zdXJlRW52TG9hZGVkIiwiZ2V0T3B0aW9uYWxFbnYiLCJjb25uZWN0aW9uU3RyaW5nIiwiZGIiLCJjbGllbnQiLCJtYXgiLCJpZGxlX3RpbWVvdXQiLCJjb25uZWN0X3RpbWVvdXQiLCJzc2wiLCJwcmVwYXJlIiwibW9ja0NsaWVudCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiAgentFeedback: () => (/* binding */ aiAgentFeedback),\n/* harmony export */   conversations: () => (/* binding */ conversations),\n/* harmony export */   matchCandidates: () => (/* binding */ matchCandidates),\n/* harmony export */   matchQueue: () => (/* binding */ matchQueue),\n/* harmony export */   matchRequests: () => (/* binding */ matchRequests),\n/* harmony export */   matches: () => (/* binding */ matches),\n/* harmony export */   messages: () => (/* binding */ messages),\n/* harmony export */   userProfiles: () => (/* binding */ userProfiles),\n/* harmony export */   userSessions: () => (/* binding */ userSessions),\n/* harmony export */   users: () => (/* binding */ users)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/uuid.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/jsonb.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n\n// Users table\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name'),\n    avatar: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('avatar'),\n    bio: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('bio'),\n    age: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('age'),\n    gender: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('gender'),\n    location: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('location'),\n    interests: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('interests').$type().default([]),\n    personalityTraits: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('personality_traits').$type(),\n    personalitySummary: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('personality_summary'),\n    isActive: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('is_active').default(true),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// User profiles table for additional profile information\nconst userProfiles = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('user_profiles', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user_id').references(()=>users.id).notNull(),\n    selfDescription: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('self_description'),\n    lookingFor: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('looking_for'),\n    relationshipGoals: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('relationship_goals'),\n    lifestyle: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('lifestyle').$type(),\n    values: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('values').$type().default([]),\n    photos: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('photos').$type().default([]),\n    preferences: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('preferences').$type(),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// Matches table\nconst matches = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('matches', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    user1Id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user1_id').references(()=>users.id).notNull(),\n    user2Id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user2_id').references(()=>users.id).notNull(),\n    compatibilityScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('compatibility_score'),\n    aiAnalysis: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('ai_analysis').$type(),\n    conversationSimulation: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('conversation_simulation').$type(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status').default('pending'),\n    user1Liked: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user1_liked').default(false),\n    user2Liked: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user2_liked').default(false),\n    user1Viewed: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user1_viewed').default(false),\n    user2Viewed: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('user2_viewed').default(false),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// AI Agent Feedback table\nconst aiAgentFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('ai_agent_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    matchId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('match_id').references(()=>matches.id).notNull(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user_id').references(()=>users.id).notNull(),\n    feedbackType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('feedback_type').notNull(),\n    feedbackText: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('feedback_text'),\n    rating: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('rating'),\n    aspectRated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('aspect_rated'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// Conversations table for storing chat messages\nconst conversations = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('conversations', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    matchId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('match_id').references(()=>matches.id).notNull(),\n    isActive: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('is_active').default(true),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('updated_at').defaultNow()\n});\n// Messages table\nconst messages = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('messages', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    conversationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('conversation_id').references(()=>conversations.id).notNull(),\n    senderId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('sender_id').references(()=>users.id).notNull(),\n    content: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('content').notNull(),\n    messageType: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('message_type').default('text'),\n    isRead: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.boolean)('is_read').default(false),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// User sessions for tracking activity\nconst userSessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('user_sessions', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('user_id').references(()=>users.id).notNull(),\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('session_token').notNull().unique(),\n    expiresAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('expires_at').notNull(),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// ===== V2.0 新增表 =====\n// 1. 任务队列，用于异步处理匹配请求\nconst matchQueue = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('match_queue', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    matchRequestId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('match_request_id').notNull().unique(),\n    requesterId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('requester_id').references(()=>users.id).notNull(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status').default('pending'),\n    attempts: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('attempts').default(0),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// 2. 匹配请求的总记录\nconst matchRequests = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('match_requests', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    requesterId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('requester_id').references(()=>users.id).notNull(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status').default('processing'),\n    errorMessage: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('error_message'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n// 3. 用于记录用户对每个候选人的决策和详细分析\nconst matchCandidates = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_0__.pgTable)('match_candidates', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('id').primaryKey().defaultRandom(),\n    requestId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('request_id').references(()=>matchRequests.id).notNull(),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.uuid)('candidate_id').references(()=>users.id).notNull(),\n    rank: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('rank').notNull(),\n    // 兼容性分析数据\n    compatibilityScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.integer)('compatibility_score').notNull().default(0),\n    reasoning: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('reasoning'),\n    highlights: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('highlights'),\n    challenges: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('challenges'),\n    personalitySummary: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('personality_summary'),\n    // 首席推荐的额外数据 (rank = 1 时才有)\n    relationshipInsight: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('relationship_insight'),\n    conversationSimulation: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('conversation_simulation'),\n    datePlan: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.jsonb)('date_plan'),\n    // 用户决策\n    userDecision: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('user_decision').default('pending'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.timestamp)('created_at').defaultNow()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/arag-soul/agents.ts":
/*!**********************************************!*\
  !*** ./src/lib/services/arag-soul/agents.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateFullReportNode: () => (/* binding */ generateFullReportNode),\n/* harmony export */   generateUserSoulProfileNode: () => (/* binding */ generateUserSoulProfileNode),\n/* harmony export */   rankAndFinalizeNode: () => (/* binding */ rankAndFinalizeNode),\n/* harmony export */   runCompatibilityInferenceNode: () => (/* binding */ runCompatibilityInferenceNode)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n/* harmony import */ var _prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./prompts */ \"(rsc)/./src/lib/services/arag-soul/prompts.ts\");\n/* harmony import */ var _utils_env__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/env */ \"(rsc)/./src/lib/utils/env.ts\");\n// ARAG-Soul 框架的 Agent 实现\n\n\n\n// 模型配置\nconst MODEL_NAME = 'google/gemini-2.5-flash-preview-05-20';\n// 创建 AI 调用函数\nconst createModelCall = async (prompt, systemPrompt = _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS.default)=>{\n    // 确保环境变量被加载并获取 API 密钥\n    (0,_utils_env__WEBPACK_IMPORTED_MODULE_2__.ensureEnvLoaded)();\n    const apiKey = (0,_utils_env__WEBPACK_IMPORTED_MODULE_2__.getRequiredEnv)('OPENROUTER_API_KEY');\n    // 初始化 OpenRouter 客户端\n    const openrouter = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n        baseURL: 'https://openrouter.ai/api/v1',\n        apiKey: apiKey\n    });\n    try {\n        const completion = await openrouter.chat.completions.create({\n            model: MODEL_NAME,\n            messages: [\n                {\n                    role: 'system',\n                    content: systemPrompt\n                },\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ],\n            temperature: 0.7,\n            max_tokens: 2048,\n            response_format: {\n                type: 'json_object'\n            }\n        });\n        return completion.choices[0]?.message?.content || '';\n    } catch (error) {\n        console.error('OpenRouter API 调用失败:', error);\n        throw error;\n    }\n};\n// 人格洞察 Agent\nasync function generateUserSoulProfileNode(state) {\n    try {\n        console.log('🧠 执行人格洞察 Agent...');\n        const { userProfile } = state;\n        if (!userProfile) {\n            throw new Error('用户资料不存在');\n        }\n        const prompt = _prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPTS.personalityInsight.replace('{name}', userProfile.name || '未知').replace('{age}', userProfile.age?.toString() || '未知').replace('{gender}', userProfile.gender || '未知').replace('{selfDescription}', userProfile.selfDescription || '').replace('{interests}', JSON.stringify(userProfile.interests || [])).replace('{values}', JSON.stringify(userProfile.values || [])).replace('{lifestyle}', JSON.stringify(userProfile.lifestyle || {})).replace('{relationshipGoals}', userProfile.relationshipGoals || '');\n        const responseContent = await createModelCall(prompt, _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS.personality);\n        console.log('🔍 AI 返回的原始内容:', responseContent);\n        let result;\n        try {\n            result = JSON.parse(responseContent);\n        } catch (parseError) {\n            console.error('❌ JSON 解析失败:', parseError);\n            console.error('原始内容:', responseContent);\n            throw new Error(`人格洞察 JSON 解析失败: ${parseError}`);\n        }\n        console.log('🔍 解析后的结果:', result);\n        // 添加安全检查和默认值\n        const userSoulProfile = {\n            userId: userProfile.userId,\n            personalityTraits: result.personalityTraits || {},\n            coreValues: Array.isArray(result.coreValues) ? result.coreValues : [],\n            communicationStyle: result.communicationStyle || '未知',\n            relationshipGoals: userProfile.relationshipGoals || '寻找合适的伴侣',\n            lifestyle: userProfile.lifestyle || {},\n            summary: result.summary || '正在分析中...'\n        };\n        console.log('🔍 生成的用户灵魂档案:', {\n            userId: userSoulProfile.userId,\n            coreValuesCount: userSoulProfile.coreValues.length,\n            hasSummary: !!userSoulProfile.summary\n        });\n        console.log('✅ 人格洞察完成');\n        // 打印当前状态\n        console.log('当前状态:', state);\n        return {\n            userSoulProfile,\n            step: 'personality_insight_completed'\n        };\n    } catch (error) {\n        console.error('❌ 人格洞察失败:', error);\n        return {\n            error: `人格洞察失败: ${error instanceof Error ? error.message : '未知错误'}`,\n            step: 'personality_insight_failed'\n        };\n    }\n}\n// 深度兼容性推理 Agent\nasync function runCompatibilityInferenceNode(state) {\n    try {\n        console.log('🔍 执行兼容性推理 Agent...');\n        const { userSoulProfile, candidatePoolIds } = state;\n        if (!userSoulProfile || !candidatePoolIds) {\n            throw new Error('缺少必要的状态数据');\n        }\n        // 这里需要从数据库获取候选人资料\n        // 为了演示，我们先创建一个模拟的候选人分析函数\n        const candidatesWithAnalysis = [];\n        // 并行处理所有候选人\n        const analysisPromises = candidatePoolIds.map(async (candidateId)=>{\n            // TODO: 从数据库获取候选人详细资料\n            const candidateProfile = await getCandidateProfile(candidateId);\n            const prompt = _prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPTS.compatibilityInference.replace('{userSoulProfile}', JSON.stringify(userSoulProfile)).replace('{candidateName}', candidateProfile.name || '未知').replace('{candidateAge}', candidateProfile.age?.toString() || '未知').replace('{candidateSelfDescription}', candidateProfile.selfDescription || '').replace('{candidateInterests}', JSON.stringify(candidateProfile.interests || [])).replace('{candidateValues}', JSON.stringify(candidateProfile.values || [])).replace('{candidateLifestyle}', JSON.stringify(candidateProfile.lifestyle || {}));\n            const responseContent = await createModelCall(prompt, _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS.compatibility);\n            const result = JSON.parse(responseContent);\n            return {\n                candidateId,\n                compatibilityScore: result.compatibilityScore || 0,\n                reasoning: result.reasoning || '分析中...',\n                highlights: Array.isArray(result.highlights) ? result.highlights : [],\n                challenges: Array.isArray(result.challenges) ? result.challenges : [],\n                personalitySummary: result.personalitySummary || '候选人分析中...'\n            };\n        });\n        const analysisResults = await Promise.all(analysisPromises);\n        candidatesWithAnalysis.push(...analysisResults);\n        console.log(`✅ 兼容性推理完成，分析了 ${candidatesWithAnalysis.length} 个候选人`);\n        return {\n            candidatesWithAnalysis,\n            step: 'compatibility_inference_completed'\n        };\n    } catch (error) {\n        console.error('❌ 兼容性推理失败:', error);\n        return {\n            error: `兼容性推理失败: ${error instanceof Error ? error.message : '未知错误'}`,\n            step: 'compatibility_inference_failed'\n        };\n    }\n}\n// 从数据库获取候选人资料\nasync function getCandidateProfile(candidateId) {\n    const { db } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\"));\n    const { users, userProfiles } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\"));\n    const { eq } = await __webpack_require__.e(/*! import() */ \"vendor-chunks/drizzle-orm\").then(__webpack_require__.bind(__webpack_require__, /*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/index.js\"));\n    const candidateWithProfile = await db.select({\n        user: users,\n        profile: userProfiles\n    }).from(users).leftJoin(userProfiles, eq(users.id, userProfiles.userId)).where(eq(users.id, candidateId)).limit(1);\n    if (candidateWithProfile.length === 0) {\n        throw new Error(`候选人 ${candidateId} 不存在`);\n    }\n    const candidate = candidateWithProfile[0];\n    return {\n        userId: candidate.user.id,\n        name: candidate.user.name || '未知',\n        age: candidate.user.age || 0,\n        selfDescription: candidate.profile?.selfDescription || '',\n        interests: candidate.user.interests || [],\n        values: candidate.profile?.values || [],\n        lifestyle: candidate.profile?.lifestyle || {}\n    };\n}\n// 排序和最终决策 Agent\nasync function rankAndFinalizeNode(state) {\n    try {\n        console.log('🏆 执行排序和最终决策 Agent...');\n        const { candidatesWithAnalysis } = state;\n        if (!candidatesWithAnalysis || candidatesWithAnalysis.length === 0) {\n            throw new Error('没有候选人分析数据');\n        }\n        // 按兼容性分数排序\n        const rankedCandidates = candidatesWithAnalysis.sort((a, b)=>b.compatibilityScore - a.compatibilityScore).slice(0, 5); // 取前5名\n        console.log(`✅ 排序完成，选出前 ${rankedCandidates.length} 名候选人`);\n        return {\n            rankedCandidates,\n            step: 'ranking_completed'\n        };\n    } catch (error) {\n        console.error('❌ 排序失败:', error);\n        return {\n            error: `排序失败: ${error instanceof Error ? error.message : '未知错误'}`,\n            step: 'ranking_failed'\n        };\n    }\n}\n// 生成完整报告 Agent\nasync function generateFullReportNode(state) {\n    try {\n        console.log('📝 生成完整报告...');\n        const { rankedCandidates, userSoulProfile } = state;\n        if (!rankedCandidates || !userSoulProfile) {\n            throw new Error('缺少必要数据');\n        }\n        // 为首席推荐生成完整报告\n        const topCandidate = rankedCandidates[0];\n        // 生成关系洞察、对话模拟和约会计划\n        const [relationshipInsight, conversationSimulation, datePlan] = await Promise.all([\n            generateRelationshipInsight(userSoulProfile, topCandidate),\n            generateConversationSimulation(userSoulProfile, topCandidate),\n            generateDatePlan(userSoulProfile, topCandidate)\n        ]);\n        const finalMatrix = {\n            topMatch: {\n                candidate: topCandidate,\n                relationshipInsight,\n                conversationSimulation,\n                datePlan\n            },\n            potentialMatches: rankedCandidates.slice(1, 5).map((candidate)=>({\n                    candidate,\n                    highlights: candidate.highlights,\n                    compatibilityReason: candidate.reasoning\n                })),\n            generatedAt: new Date(),\n            requestId: state.requesterId\n        };\n        console.log('✅ 完整报告生成完成');\n        return {\n            finalMatrix,\n            step: 'report_generation_completed'\n        };\n    } catch (error) {\n        console.error('❌ 报告生成失败:', error);\n        return {\n            error: `报告生成失败: ${error instanceof Error ? error.message : '未知错误'}`,\n            step: 'report_generation_failed'\n        };\n    }\n}\n// 辅助函数：生成关系洞察\nasync function generateRelationshipInsight(userProfile, candidate) {\n    const prompt = _prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPTS.relationshipHighlight.replace('{userSoulProfile}', JSON.stringify(userProfile)).replace('{candidateAnalysis}', JSON.stringify(candidate));\n    const responseContent = await createModelCall(prompt, _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS.compatibility);\n    return JSON.parse(responseContent);\n}\n// 辅助函数：生成对话模拟\nasync function generateConversationSimulation(userProfile, candidate) {\n    const prompt = _prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPTS.conversationSimulation.replace('{userProfile}', JSON.stringify(userProfile)).replace('{candidateProfile}', JSON.stringify(candidate)).replace('{scenario}', '咖啡厅初次见面');\n    const responseContent = await createModelCall(prompt, _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS.conversation);\n    return JSON.parse(responseContent);\n}\n// 辅助函数：生成约会计划\nasync function generateDatePlan(userProfile, candidate) {\n    // 安全地找出共同兴趣\n    const userCoreValues = userProfile.coreValues || [];\n    const candidateHighlights = candidate.highlights || [];\n    const commonInterests = userCoreValues.filter((value)=>candidateHighlights.some((highlight)=>highlight.includes(value)));\n    const prompt = _prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPTS.datePlanGeneration.replace('{userProfile}', JSON.stringify(userProfile)).replace('{candidateProfile}', JSON.stringify(candidate)).replace('{commonInterests}', JSON.stringify(commonInterests));\n    const responseContent = await createModelCall(prompt, _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS.dating);\n    console.log(responseContent);\n    return JSON.parse(responseContent);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/arag-soul/agents.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/arag-soul/index.ts":
/*!*********************************************!*\
  !*** ./src/lib/services/arag-soul/index.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AragSoulService: () => (/* reexport safe */ _workflow__WEBPACK_IMPORTED_MODULE_3__.AragSoulService),\n/* harmony export */   AragSoulStateAnnotation: () => (/* reexport safe */ _workflow__WEBPACK_IMPORTED_MODULE_3__.AragSoulStateAnnotation),\n/* harmony export */   PROMPTS: () => (/* reexport safe */ _prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPTS),\n/* harmony export */   SYSTEM_PROMPTS: () => (/* reexport safe */ _prompts__WEBPACK_IMPORTED_MODULE_1__.SYSTEM_PROMPTS),\n/* harmony export */   aragSoulService: () => (/* reexport safe */ _workflow__WEBPACK_IMPORTED_MODULE_3__.aragSoulService),\n/* harmony export */   createAragSoulWorkflow: () => (/* reexport safe */ _workflow__WEBPACK_IMPORTED_MODULE_3__.createAragSoulWorkflow),\n/* harmony export */   \"default\": () => (/* reexport safe */ _workflow__WEBPACK_IMPORTED_MODULE_3__.aragSoulService),\n/* harmony export */   generateFullReportNode: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_2__.generateFullReportNode),\n/* harmony export */   generateUserSoulProfileNode: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_2__.generateUserSoulProfileNode),\n/* harmony export */   rankAndFinalizeNode: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_2__.rankAndFinalizeNode),\n/* harmony export */   runCompatibilityInferenceNode: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_2__.runCompatibilityInferenceNode)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(rsc)/./src/lib/services/arag-soul/types.ts\");\n/* harmony import */ var _prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./prompts */ \"(rsc)/./src/lib/services/arag-soul/prompts.ts\");\n/* harmony import */ var _agents__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./agents */ \"(rsc)/./src/lib/services/arag-soul/agents.ts\");\n/* harmony import */ var _workflow__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./workflow */ \"(rsc)/./src/lib/services/arag-soul/workflow.ts\");\n// ARAG-Soul 框架主入口\n\n\n\n\n// 便捷导出\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3NlcnZpY2VzL2FyYWctc291bC9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxrQkFBa0I7QUFFTTtBQUNFO0FBQ0Q7QUFDRTtBQUUzQixPQUFPO0FBQ2lEIiwic291cmNlcyI6WyIvaG9tZS91YnQyMi93b3Jrc3BhY2UvaW5kaWUvbGluZ3hpYWktZ2VtaW5pL3NyYy9saWIvc2VydmljZXMvYXJhZy1zb3VsL2luZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEFSQUctU291bCDmoYbmnrbkuLvlhaXlj6NcblxuZXhwb3J0ICogZnJvbSAnLi90eXBlcyc7XG5leHBvcnQgKiBmcm9tICcuL3Byb21wdHMnO1xuZXhwb3J0ICogZnJvbSAnLi9hZ2VudHMnO1xuZXhwb3J0ICogZnJvbSAnLi93b3JrZmxvdyc7XG5cbi8vIOS+v+aNt+WvvOWHulxuZXhwb3J0IHsgYXJhZ1NvdWxTZXJ2aWNlIGFzIGRlZmF1bHQgfSBmcm9tICcuL3dvcmtmbG93JztcbiJdLCJuYW1lcyI6WyJhcmFnU291bFNlcnZpY2UiLCJkZWZhdWx0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/arag-soul/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/arag-soul/prompts.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/arag-soul/prompts.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROMPTS: () => (/* binding */ PROMPTS),\n/* harmony export */   SYSTEM_PROMPTS: () => (/* binding */ SYSTEM_PROMPTS)\n/* harmony export */ });\n// ARAG-Soul 框架的 Prompt 模板\nconst PROMPTS = {\n    // 人格洞察 Agent\n    personalityInsight: `\n你是一位专业的心理学家和人格分析师。请基于用户的详细资料，生成一份深度的\"灵魂画像\"。\n\n用户资料：\n姓名: {name}\n年龄: {age}\n性别: {gender}\n自我描述: {selfDescription}\n兴趣爱好: {interests}\n价值观: {values}\n生活方式: {lifestyle}\n感情目标: {relationshipGoals}\n\n请分析并生成：\n1. 核心人格特质（5-7个关键词）\n2. 沟通风格描述\n3. 价值观体系\n4. 情感需求\n5. 生活态度\n6. 一句话人格总结\n\n请以JSON格式返回：\n{\n  \"personalityTraits\": {\n    \"openness\": 0.8,\n    \"conscientiousness\": 0.7,\n    \"extraversion\": 0.6,\n    \"agreeableness\": 0.9,\n    \"neuroticism\": 0.3\n  },\n  \"coreValues\": [\"诚实\", \"成长\", \"家庭\"],\n  \"communicationStyle\": \"温和而深度的交流者\",\n  \"emotionalNeeds\": [\"理解\", \"支持\", \"共同成长\"],\n  \"lifeAttitude\": \"积极向上，注重内在成长\",\n  \"summary\": \"一个温暖、有深度、追求真实连接的灵魂\"\n}\n`,\n    // 深度兼容性推理 Agent\n    compatibilityInference: `\n你是一位资深的情感匹配专家。请分析两个人的兼容性。\n\n用户A的灵魂画像：\n{userSoulProfile}\n\n候选人B的资料：\n姓名: {candidateName}\n年龄: {candidateAge}\n自我描述: {candidateSelfDescription}\n兴趣爱好: {candidateInterests}\n价值观: {candidateValues}\n生活方式: {candidateLifestyle}\n\n请进行深度分析并给出：\n1. 兼容性分数 (0-100)\n2. 详细推理过程\n3. 关系亮点 (3-5个)\n4. 潜在挑战 (2-3个)\n5. 候选人人格摘要\n\n请以JSON格式返回：\n{\n  \"compatibilityScore\": 85,\n  \"reasoning\": \"详细的兼容性分析...\",\n  \"highlights\": [\"共同的价值观\", \"互补的性格\", \"相似的生活目标\"],\n  \"challenges\": [\"沟通方式差异\", \"生活节奏不同\"],\n  \"personalitySummary\": \"一个温暖、独立、有创造力的人\"\n}\n`,\n    // 关系亮点提炼 Agent\n    relationshipHighlight: `\n基于兼容性分析，请深入挖掘这段关系的潜力。\n\n用户A: {userSoulProfile}\n候选人B: {candidateAnalysis}\n\n请提供：\n1. 关系优势 (3-4个具体方面)\n2. 成长机会 (2-3个)\n3. 相处建议 (3-4条实用建议)\n4. 沟通技巧 (2-3个针对性建议)\n\n请以JSON格式返回：\n{\n  \"strengths\": [\"深度的精神连接\", \"互补的技能组合\"],\n  \"growthOpportunities\": [\"共同探索新兴趣\", \"相互学习不同视角\"],\n  \"suggestions\": [\"定期深度对话\", \"尊重彼此的独立空间\"],\n  \"communicationTips\": [\"使用'我'语句表达感受\", \"积极倾听对方观点\"]\n}\n`,\n    // 对话模拟 Agent\n    conversationSimulation: `\n请模拟用户A和候选人B的一段自然对话。\n\n用户A资料: {userProfile}\n候选人B资料: {candidateProfile}\n对话场景: {scenario}\n\n要求：\n1. 对话要体现双方的性格特点\n2. 包含6-8轮对话\n3. 展现自然的互动和化学反应\n4. 体现共同兴趣或价值观\n\n请以JSON格式返回：\n{\n  \"scenario\": \"根据双方特点选择的地点，如咖啡厅\",\n  \"messages\": [\n    {\"speaker\": \"user\", \"content\": \"...\", \"emotion\": \"好奇\"},\n    {\"speaker\": \"candidate\", \"content\": \"...\", \"emotion\": \"友善\"}\n  ],\n  \"analysis\": \"这段对话展现了双方的...\"\n}\n`,\n    // 约会计划生成 Agent\n    datePlanGeneration: `\n基于两人的共同兴趣和性格特点，设计一个完美的初次约会计划。\n\n用户A: {userProfile}\n候选人B: {candidateProfile}\n共同兴趣: {commonInterests}\n\n请设计：\n1. 约会主题和地点\n2. 具体活动安排\n3. 时间规划\n4. 预算建议\n5. 为什么这个计划适合他们\n\n请以JSON格式返回：\n{\n  \"title\": \"艺术与咖啡的邂逅\",\n  \"description\": \"结合艺术欣赏和深度交流的约会\",\n  \"location\": \"市中心艺术区\",\n  \"activities\": [\"参观画廊\", \"咖啡厅聊天\", \"街头艺术漫步\"],\n  \"duration\": \"3-4小时\",\n  \"budget\": \"200-300元\",\n  \"reasoning\": \"这个计划结合了双方对艺术的热爱...\"\n}\n`\n};\n// 系统提示词\nconst SYSTEM_PROMPTS = {\n    default: `你是寡佬AI的专业红娘助手，专门负责深度的情感匹配分析。你的分析要准确、深入、有洞察力，同时保持温暖和专业的语调。请始终以JSON格式返回结构化的结果。`,\n    personality: `你是一位专业的心理学家，擅长人格分析和深度洞察。你的分析要基于心理学理论，同时具有实用性。`,\n    compatibility: `你是一位资深的情感匹配专家，拥有丰富的关系咨询经验。你的分析要客观、全面，既看到优势也识别挑战。`,\n    conversation: `你是一位对话专家，擅长模拟真实的人际互动。你的对话要自然、有趣，体现人物的真实性格。`,\n    dating: `你是一位约会策划专家，了解各种约会形式和场所。你的建议要实用、有创意，适合不同性格的人。`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/arag-soul/prompts.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/arag-soul/types.ts":
/*!*********************************************!*\
  !*** ./src/lib/services/arag-soul/types.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n// ARAG-Soul 框架的类型定义\n// Prompt 模板\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/arag-soul/types.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/arag-soul/workflow.ts":
/*!************************************************!*\
  !*** ./src/lib/services/arag-soul/workflow.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AragSoulService: () => (/* binding */ AragSoulService),\n/* harmony export */   AragSoulStateAnnotation: () => (/* binding */ AragSoulStateAnnotation),\n/* harmony export */   aragSoulService: () => (/* binding */ aragSoulService),\n/* harmony export */   createAragSoulWorkflow: () => (/* binding */ createAragSoulWorkflow)\n/* harmony export */ });\n/* harmony import */ var _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @langchain/langgraph */ \"(rsc)/./node_modules/@langchain/langgraph/index.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var _agents__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./agents */ \"(rsc)/./src/lib/services/arag-soul/agents.ts\");\n// ARAG-Soul 工作流编排器\n // 暂时不使用\n\n\n\n\n// 定义 ARAG-Soul 状态注解\nconst AragSoulStateAnnotation = _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation.Root({\n    // 输入\n    requesterId: _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation,\n    userProfile: _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation,\n    candidatePoolIds: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x,\n        default: ()=>[]\n    }),\n    // 中间状态\n    userSoulProfile: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x\n    }),\n    candidatesWithAnalysis: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x,\n        default: ()=>[]\n    }),\n    rankedCandidates: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x,\n        default: ()=>[]\n    }),\n    // 输出\n    finalMatrix: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x\n    }),\n    // 错误处理\n    error: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x\n    }),\n    step: (0,_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.Annotation)({\n        reducer: (x, y)=>y ?? x,\n        default: ()=>'initialized'\n    })\n});\n// 候选人检索节点\nasync function retrieveCandidatesNode(state) {\n    try {\n        console.log('🔍 检索候选人池...');\n        const { requesterId } = state;\n        if (!requesterId) {\n            throw new Error('缺少请求者ID');\n        }\n        // 获取用户资料\n        const userWithProfile = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select({\n            user: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users,\n            profile: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.userProfiles\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users).leftJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.userProfiles, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.userProfiles.userId)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id, requesterId)).limit(1);\n        if (userWithProfile.length === 0) {\n            throw new Error('用户不存在');\n        }\n        const userProfile = {\n            userId: userWithProfile[0].user.id,\n            name: userWithProfile[0].user.name,\n            age: userWithProfile[0].user.age,\n            gender: userWithProfile[0].user.gender,\n            interests: userWithProfile[0].user.interests,\n            selfDescription: userWithProfile[0].profile?.selfDescription,\n            values: userWithProfile[0].profile?.values,\n            lifestyle: userWithProfile[0].profile?.lifestyle,\n            relationshipGoals: userWithProfile[0].profile?.relationshipGoals\n        };\n        // 检索候选人池（排除自己，选择异性，活跃用户）\n        const candidatePool = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select({\n            id: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users).leftJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.userProfiles, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.userProfiles.userId)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.ne)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id, requesterId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.isActive, true), // 异性匹配逻辑\n        userProfile.gender === 'male' ? (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.gender, 'female') : (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.gender, 'male'))).limit(8); // 限制候选人池大小\n        const candidatePoolIds = candidatePool.map((c)=>c.id);\n        console.log(`✅ 检索到 ${candidatePoolIds.length} 个候选人`);\n        return {\n            userProfile,\n            candidatePoolIds,\n            step: 'candidates_retrieved'\n        };\n    } catch (error) {\n        console.error('❌ 候选人检索失败:', error);\n        return {\n            error: `候选人检索失败: ${error instanceof Error ? error.message : '未知错误'}`,\n            step: 'candidates_retrieval_failed'\n        };\n    }\n}\n// 条件路由函数\nfunction shouldContinue(state) {\n    if (state.error) {\n        return '__end__';\n    }\n    switch(state.step){\n        case 'candidates_retrieved':\n            return 'generateUserSoulProfile';\n        case 'personality_insight_completed':\n            return 'runCompatibilityInference';\n        case 'compatibility_inference_completed':\n            return 'rankAndFinalize';\n        case 'ranking_completed':\n            return 'generateFullReport';\n        case 'report_generation_completed':\n            return '__end__';\n        default:\n            return '__end__';\n    }\n}\n// 创建 ARAG-Soul 工作流\nfunction createAragSoulWorkflow() {\n    const workflow = new _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.StateGraph(AragSoulStateAnnotation)// 添加节点\n    .addNode('retrieveCandidates', retrieveCandidatesNode).addNode('generateUserSoulProfile', _agents__WEBPACK_IMPORTED_MODULE_3__.generateUserSoulProfileNode).addNode('runCompatibilityInference', _agents__WEBPACK_IMPORTED_MODULE_3__.runCompatibilityInferenceNode).addNode('rankAndFinalize', _agents__WEBPACK_IMPORTED_MODULE_3__.rankAndFinalizeNode).addNode('generateFullReport', _agents__WEBPACK_IMPORTED_MODULE_3__.generateFullReportNode)// 设置入口点\n    .addEdge(_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.START, 'retrieveCandidates')// 添加条件边\n    .addConditionalEdges('retrieveCandidates', shouldContinue, {\n        'generateUserSoulProfile': 'generateUserSoulProfile',\n        '__end__': _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END\n    }).addConditionalEdges('generateUserSoulProfile', shouldContinue, {\n        'runCompatibilityInference': 'runCompatibilityInference',\n        '__end__': _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END\n    }).addConditionalEdges('runCompatibilityInference', shouldContinue, {\n        'rankAndFinalize': 'rankAndFinalize',\n        '__end__': _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END\n    }).addConditionalEdges('rankAndFinalize', shouldContinue, {\n        'generateFullReport': 'generateFullReport',\n        '__end__': _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END\n    }).addConditionalEdges('generateFullReport', shouldContinue, {\n        '__end__': _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END\n    });\n    return workflow.compile();\n}\n// ARAG-Soul 服务主入口\nclass AragSoulService {\n    constructor(){\n        this.workflow = createAragSoulWorkflow();\n    }\n    async generateCandidateMatrix(requesterId) {\n        try {\n            console.log(`🚀 启动 ARAG-Soul 工作流，请求者: ${requesterId}`);\n            const initialState = {\n                requesterId,\n                step: 'initialized'\n            };\n            // 执行工作流\n            const result = await this.workflow.invoke(initialState);\n            if (result.error) {\n                throw new Error(result.error);\n            }\n            if (!result.finalMatrix) {\n                throw new Error('工作流未生成最终结果');\n            }\n            console.log('✅ ARAG-Soul 工作流执行完成');\n            return result.finalMatrix;\n        } catch (error) {\n            console.error('❌ ARAG-Soul 工作流执行失败:', error);\n            throw error;\n        }\n    }\n    // 流式执行（用于实时监控）\n    async *generateCandidateMatrixStream(requesterId) {\n        const initialState = {\n            requesterId,\n            step: 'initialized'\n        };\n        try {\n            for await (const step of this.workflow.stream(initialState)){\n                yield {\n                    step: step.step || 'processing',\n                    data: step,\n                    timestamp: new Date()\n                };\n            }\n        } catch (error) {\n            yield {\n                step: 'error',\n                error: error instanceof Error ? error.message : '未知错误',\n                timestamp: new Date()\n            };\n        }\n    }\n}\n// 导出单例实例\nconst aragSoulService = new AragSoulService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/arag-soul/workflow.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/matching-v2.ts":
/*!*****************************************!*\
  !*** ./src/lib/services/matching-v2.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MatchingServiceV2: () => (/* binding */ MatchingServiceV2)\n/* harmony export */ });\n/* harmony import */ var _lib_utils_env__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils/env */ \"(rsc)/./src/lib/utils/env.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var _arag_soul__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./arag-soul */ \"(rsc)/./src/lib/services/arag-soul/index.ts\");\n// V2.0 匹配服务 - 智能候选人矩阵\n\n\n\n\n\n// 确保环境变量被加载\n(0,_lib_utils_env__WEBPACK_IMPORTED_MODULE_0__.ensureEnvLoaded)();\nclass MatchingServiceV2 {\n    /**\n   * 处理队列中的匹配请求\n   */ static async processQueuedRequests() {\n        try {\n            console.log('🔄 开始处理匹配队列...');\n            // 获取待处理的任务\n            const pendingTasks = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue.status, 'pending')).limit(5); // 一次处理5个任务\n            if (pendingTasks.length === 0) {\n                console.log('📭 队列为空，无待处理任务');\n                return {\n                    processedCount: 0\n                };\n            }\n            console.log(`📋 发现 ${pendingTasks.length} 个待处理任务`);\n            // 并行处理任务\n            const processingPromises = pendingTasks.map((task)=>this.processSingleRequest(task.matchRequestId, task.requesterId));\n            const results = await Promise.allSettled(processingPromises);\n            const successCount = results.filter((result)=>result.status === 'fulfilled').length;\n            console.log(`✅ 队列处理完成，成功处理 ${successCount}/${pendingTasks.length} 个任务`);\n            return {\n                processedCount: successCount\n            };\n        } catch (error) {\n            console.error('❌ 队列处理失败:', error);\n            return {\n                processedCount: 0\n            };\n        }\n    }\n    /**\n   * 处理单个匹配请求\n   */ static async processSingleRequest(requestId, requesterId) {\n        try {\n            console.log(`🎯 开始处理请求: ${requestId}`);\n            // 更新队列状态为处理中\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue).set({\n                status: 'processing'\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue.matchRequestId, requestId));\n            // 更新请求状态为处理中\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).set({\n                status: 'processing'\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id, requestId));\n            // 调用 ARAG-Soul 生成候选人矩阵\n            const candidateMatrix = await _arag_soul__WEBPACK_IMPORTED_MODULE_3__.aragSoulService.generateCandidateMatrix(requesterId);\n            // 保存结果到数据库\n            await this.saveCandidateMatrix(requestId, candidateMatrix);\n            // 更新状态为完成\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).set({\n                status: 'completed'\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id, requestId));\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue).set({\n                status: 'completed'\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue.matchRequestId, requestId));\n            console.log(`✅ 请求处理完成: ${requestId}`);\n        } catch (error) {\n            console.error(`❌ 请求处理失败: ${requestId}`, error);\n            // 更新状态为失败\n            const errorMessage = error instanceof Error ? error.message : '未知错误';\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).set({\n                status: 'failed',\n                errorMessage\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id, requestId));\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue).set({\n                status: 'failed'\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchQueue.matchRequestId, requestId));\n        }\n    }\n    /**\n   * 保存候选人矩阵到数据库\n   */ static async saveCandidateMatrix(requestId, matrix) {\n        try {\n            // 保存首席推荐（包含完整数据）\n            const topCandidateData = {\n                requestId,\n                candidateId: matrix.topMatch.candidate.candidateId,\n                rank: 1,\n                compatibilityScore: matrix.topMatch.candidate.compatibilityScore,\n                reasoning: matrix.topMatch.candidate.reasoning,\n                highlights: matrix.topMatch.candidate.highlights,\n                challenges: matrix.topMatch.candidate.challenges,\n                personalitySummary: matrix.topMatch.candidate.personalitySummary,\n                // 首席推荐的额外数据\n                relationshipInsight: matrix.topMatch.relationshipInsight,\n                conversationSimulation: matrix.topMatch.conversationSimulation,\n                datePlan: matrix.topMatch.datePlan,\n                userDecision: 'pending'\n            };\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates).values(topCandidateData);\n            // 保存潜力候选人（简化数据）\n            const potentialCandidatesData = matrix.potentialMatches.map((match, index)=>({\n                    requestId,\n                    candidateId: match.candidate.candidateId,\n                    rank: index + 2,\n                    compatibilityScore: match.candidate.compatibilityScore,\n                    reasoning: match.candidate.reasoning,\n                    highlights: match.candidate.highlights,\n                    challenges: match.candidate.challenges,\n                    personalitySummary: match.candidate.personalitySummary,\n                    // 潜力候选人不需要额外数据\n                    relationshipInsight: null,\n                    conversationSimulation: null,\n                    datePlan: null,\n                    userDecision: 'pending'\n                }));\n            if (potentialCandidatesData.length > 0) {\n                await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates).values(potentialCandidatesData);\n            }\n            console.log(`💾 候选人矩阵已保存: ${requestId}`);\n        } catch (error) {\n            console.error('保存候选人矩阵失败:', error);\n            throw error;\n        }\n    }\n    /**\n   * 更新用户对候选人的决策（单向匹配逻辑）\n   */ static async updateCandidateDecision(requestId, candidateId, userId, decision) {\n        try {\n            // 验证权限\n            const request = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id, requestId)).limit(1);\n            if (request.length === 0 || request[0].requesterId !== userId) {\n                throw new Error('无权限操作此请求');\n            }\n            // 更新决策\n            await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates).set({\n                userDecision: decision\n            }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.requestId, requestId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.candidateId, candidateId)));\n            // 如果是喜欢，检查候选人是否已经回应了这个喜欢\n            let mutualMatch = false;\n            let contactInfo = null;\n            if (decision === 'liked') {\n                // 检查候选人是否也喜欢了发起者\n                const candidateResponse = await this.checkCandidateResponse(userId, candidateId);\n                if (candidateResponse.hasLiked) {\n                    // 互相喜欢，更新状态并获取联系信息\n                    mutualMatch = true;\n                    // 更新双方的状态为 mutual_liked\n                    await this.updateMutualMatchStatus(userId, candidateId);\n                    // 获取联系信息\n                    contactInfo = await this.getContactInfo(userId, candidateId);\n                }\n            }\n            return {\n                success: true,\n                mutualMatch,\n                contactInfo\n            };\n        } catch (error) {\n            console.error('更新候选人决策失败:', error);\n            throw error;\n        }\n    }\n    /**\n   * 检查候选人是否已经回应了发起者的喜欢\n   */ static async checkCandidateResponse(requesterId, candidateId) {\n        try {\n            // 查找候选人是否在任何匹配请求中喜欢了发起者\n            const candidateResponse = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.requestId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.requesterId, candidateId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.candidateId, requesterId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.userDecision, 'liked')));\n            return {\n                hasLiked: candidateResponse.length > 0\n            };\n        } catch (error) {\n            console.error('检查候选人回应失败:', error);\n            return {\n                hasLiked: false\n            };\n        }\n    }\n    /**\n   * 更新互相匹配的状态\n   */ static async updateMutualMatchStatus(userId, candidateId) {\n        try {\n            // 查找并更新用户对候选人的记录\n            const userToCandidate = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select({\n                id: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.id\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.requestId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.requesterId, userId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.candidateId, candidateId)));\n            if (userToCandidate.length > 0) {\n                await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates).set({\n                    userDecision: 'mutual_liked'\n                }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.id, userToCandidate[0].id));\n            }\n            // 查找并更新候选人对用户的记录\n            const candidateToUser = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select({\n                id: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.id\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.requestId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.requesterId, candidateId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.candidateId, userId)));\n            if (candidateToUser.length > 0) {\n                await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates).set({\n                    userDecision: 'mutual_liked'\n                }).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.id, candidateToUser[0].id));\n            }\n        } catch (error) {\n            console.error('更新互相匹配状态失败:', error);\n            throw error;\n        }\n    }\n    /**\n   * 获取联系信息\n   */ static async getContactInfo(userId, candidateId) {\n        try {\n            // 获取双方的联系信息\n            const userList = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select({\n                id: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id,\n                name: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.name,\n                email: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.email\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.or)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id, userId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.users.id, candidateId)));\n            return {\n                users: userList,\n                message: '恭喜！你们互相喜欢了！现在可以开始联系了。'\n            };\n        } catch (error) {\n            console.error('获取联系信息失败:', error);\n            return null;\n        }\n    }\n    /**\n   * 获取用户的匹配历史\n   */ static async getUserMatchHistory(userId, limit = 10) {\n        try {\n            const history = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select({\n                request: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests,\n                candidates: _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).leftJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.requestId)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.requesterId, userId)).orderBy(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.createdAt).limit(limit);\n            return history;\n        } catch (error) {\n            console.error('获取匹配历史失败:', error);\n            throw error;\n        }\n    }\n    /**\n   * 获取匹配统计信息\n   */ static async getMatchingStats(userId) {\n        try {\n            // 总请求数\n            const totalRequests = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.requesterId, userId));\n            // 成功的匹配数（至少喜欢一个候选人）\n            const successfulMatches = await _lib_db__WEBPACK_IMPORTED_MODULE_1__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.requestId)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchRequests.requesterId, userId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_2__.matchCandidates.userDecision, 'liked')));\n            // 互相匹配数\n            // 这里需要更复杂的查询，暂时简化\n            const mutualMatches = 0; // TODO: 实现互相匹配统计\n            return {\n                totalRequests: totalRequests.length,\n                successfulMatches: successfulMatches.length,\n                mutualMatches,\n                successRate: totalRequests.length > 0 ? (successfulMatches.length / totalRequests.length * 100).toFixed(1) : '0'\n            };\n        } catch (error) {\n            console.error('获取匹配统计失败:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/matching-v2.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   createRouteClient: () => (/* binding */ createRouteClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(rsc)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nconst createClient = ()=>{\n    if (false) {}\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createServerComponentClient)({\n        cookies: ()=>cookieStore\n    });\n};\nconst createRouteClient = ()=>{\n    if (false) {}\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createRouteHandlerClient)({\n        cookies: ()=>cookieStore\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/env.ts":
/*!******************************!*\
  !*** ./src/lib/utils/env.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureEnvLoaded: () => (/* binding */ ensureEnvLoaded),\n/* harmony export */   getOptionalEnv: () => (/* binding */ getOptionalEnv),\n/* harmony export */   getRequiredEnv: () => (/* binding */ getRequiredEnv),\n/* harmony export */   validateRequiredEnvVars: () => (/* binding */ validateRequiredEnvVars)\n/* harmony export */ });\n// 环境变量加载工具\nlet envLoaded = false;\nfunction ensureEnvLoaded() {\n    if (envLoaded || \"undefined\" !== 'undefined') {\n        return;\n    }\n    try {\n        // 尝试加载 .env 文件\n        (__webpack_require__(/*! dotenv */ \"(rsc)/./node_modules/dotenv/lib/main.js\").config)({\n            path: '.env'\n        });\n        envLoaded = true;\n        console.log('✅ 环境变量已加载');\n    } catch (error) {\n        // dotenv 可能不存在，尝试其他路径\n        try {\n            (__webpack_require__(/*! dotenv */ \"(rsc)/./node_modules/dotenv/lib/main.js\").config)({\n                path: '.env.local'\n            });\n            envLoaded = true;\n            console.log('✅ 环境变量已从 .env.local 加载');\n        } catch (error2) {\n            console.warn('⚠️ 无法加载 dotenv，使用系统环境变量');\n        }\n    }\n}\nfunction getRequiredEnv(key) {\n    ensureEnvLoaded();\n    const value = process.env[key];\n    if (!value) {\n        throw new Error(`环境变量 ${key} 未设置`);\n    }\n    return value;\n}\nfunction getOptionalEnv(key, defaultValue) {\n    ensureEnvLoaded();\n    return process.env[key] || defaultValue;\n}\n// 验证所有必需的环境变量\nfunction validateRequiredEnvVars() {\n    ensureEnvLoaded();\n    const required = [\n        'DATABASE_URL',\n        'OPENROUTER_API_KEY',\n        'NEXT_PUBLIC_SUPABASE_URL',\n        'NEXT_PUBLIC_SUPABASE_ANON_KEY'\n    ];\n    const missing = required.filter((key)=>!process.env[key]);\n    if (missing.length > 0) {\n        throw new Error(`缺少必需的环境变量: ${missing.join(', ')}`);\n    }\n    console.log('✅ 所有必需的环境变量都已设置');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/env.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:async_hooks":
/*!***********************************!*\
  !*** external "node:async_hooks" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:async_hooks");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("perf_hooks");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/drizzle-orm","vendor-chunks/whatwg-url","vendor-chunks/set-cookie-parser","vendor-chunks/webidl-conversions","vendor-chunks/jose","vendor-chunks/isows","vendor-chunks/postgres","vendor-chunks/dotenv","vendor-chunks/@langchain","vendor-chunks/openai","vendor-chunks/zod","vendor-chunks/semver","vendor-chunks/zod-to-json-schema","vendor-chunks/langsmith","vendor-chunks/@cfworker","vendor-chunks/uuid","vendor-chunks/retry","vendor-chunks/p-queue","vendor-chunks/js-tiktoken","vendor-chunks/mustache","vendor-chunks/p-timeout","vendor-chunks/p-retry","vendor-chunks/p-finally","vendor-chunks/eventemitter3","vendor-chunks/decamelize","vendor-chunks/camelcase","vendor-chunks/base64-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmatches%2Fmatrix%2Froute&page=%2Fapi%2Fmatches%2Fmatrix%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmatches%2Fmatrix%2Froute.ts&appDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fubt22%2Fworkspace%2Findie%2Flingxiai-gemini&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();