"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_services_matching_ts";
exports.ids = ["_rsc_src_lib_services_matching_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/services/gemini.ts":
/*!************************************!*\
  !*** ./src/lib/services/gemini.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GeminiService: () => (/* binding */ GeminiService)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\nconst openrouter = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    baseURL: 'https://openrouter.ai/api/v1',\n    apiKey: process.env.OPENROUTER_API_KEY\n});\nclass GeminiService {\n    static{\n        this.model = 'google/gemini-2.5-flash-preview-05-20';\n    }\n    static async generatePersonalitySummary(userProfile) {\n        const prompt = `\n    基于以下用户信息，生成一个详细的人格摘要：\n\n    基本信息：\n    - 姓名：${userProfile.name}\n    - 年龄：${userProfile.age}\n    - 性别：${userProfile.gender}\n    - 所在地：${userProfile.location}\n\n    个人描述：\n    - 个人简介：${userProfile.bio}\n    - 自我描述：${userProfile.selfDescription}\n    - 兴趣爱好：${userProfile.interests?.join(', ')}\n    - 寻找对象：${userProfile.lookingFor}\n    - 感情目标：${userProfile.relationshipGoals}\n\n    请分析这个人的：\n    1. 性格特征（外向/内向、开放性、责任心等）\n    2. 价值观和生活态度\n    3. 社交偏好和沟通风格\n    4. 感情需求和期望\n    5. 生活方式和兴趣匹配度\n\n    请用中文回答，格式为结构化的JSON，包含以下字段：\n    {\n      \"personalityTraits\": {\n        \"extraversion\": 0-100,\n        \"openness\": 0-100,\n        \"conscientiousness\": 0-100,\n        \"agreeableness\": 0-100,\n        \"neuroticism\": 0-100\n      },\n      \"values\": [\"价值观1\", \"价值观2\", ...],\n      \"communicationStyle\": \"沟通风格描述\",\n      \"relationshipNeeds\": \"感情需求描述\",\n      \"lifestyle\": \"生活方式描述\",\n      \"summary\": \"整体人格摘要\"\n    }\n    `;\n        try {\n            const completion = await openrouter.chat.completions.create({\n                model: this.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ],\n                response_format: {\n                    type: 'json_object'\n                }\n            });\n            const content = completion.choices[0].message.content;\n            if (content) {\n                return JSON.parse(content);\n            }\n            return {\n                error: 'Failed to parse response'\n            };\n        } catch (error) {\n            console.error('Error generating personality summary:', error.stack);\n            throw error;\n        }\n    }\n    static async simulateConversation(user1Profile, user2Profile) {\n        const prompt = `\n    模拟两个用户之间的对话，分析他们的兼容性：\n\n    用户1 - ${user1Profile.name}：\n    ${JSON.stringify(user1Profile, null, 2)}\n\n    用户2 - ${user2Profile.name}：\n    ${JSON.stringify(user2Profile, null, 2)}\n\n    请模拟他们第一次见面时的对话（8-15轮对话），并分析：\n    1. 对话流畅度\n    2. 共同话题\n    3. 价值观契合度\n    4. 沟通风格匹配\n    5. 潜在冲突点\n\n    重要：在对话中使用真实的用户名称（${user1Profile.name} 和 ${user2Profile.name}），而不是 \"user1\" 或 \"user2\"。\n\n    返回JSON格式：\n    {\n      \"conversation\": [\n        {\"speaker\": \"${user1Profile.name}\", \"message\": \"对话内容\"},\n        {\"speaker\": \"${user2Profile.name}\", \"message\": \"对话内容\"},\n        ...\n      ],\n      \"analysis\": {\n        \"conversationFlow\": 0-100,\n        \"commonTopics\": [\"话题1\", \"话题2\", ...],\n        \"valueAlignment\": 0-100,\n        \"communicationMatch\": 0-100,\n        \"potentialConflicts\": [\"冲突点1\", \"冲突点2\", ...],\n        \"overallCompatibility\": 0-100\n      }\n    }\n    `;\n        try {\n            const completion = await openrouter.chat.completions.create({\n                model: this.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ],\n                response_format: {\n                    type: 'json_object'\n                }\n            });\n            const content = completion.choices[0].message.content;\n            if (content) {\n                return JSON.parse(content);\n            }\n            return {\n                error: 'Failed to parse response'\n            };\n        } catch (error) {\n            console.error('Error simulating conversation:', error);\n            throw error;\n        }\n    }\n    static async calculateCompatibilityScore(user1Profile, user2Profile) {\n        const prompt = `\n    基于以下两个用户的资料，计算他们的兼容性分数（0-100）：\n\n    用户1：\n    ${JSON.stringify(user1Profile, null, 2)}\n\n    用户2：\n    ${JSON.stringify(user2Profile, null, 2)}\n\n    考虑因素：\n    1. 性格互补性（30%）\n    2. 价值观一致性（25%）\n    3. 兴趣爱好重叠（20%）\n    4. 生活方式匹配（15%）\n    5. 感情目标一致（10%）\n\n    只返回一个0-100之间的数字。\n    `;\n        try {\n            const completion = await openrouter.chat.completions.create({\n                model: this.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ]\n            });\n            const text = completion.choices[0].message.content || '';\n            const score = parseInt(text.match(/\\d+/)?.[0] || '0');\n            return Math.min(Math.max(score, 0), 100);\n        } catch (error) {\n            console.error('Error calculating compatibility score:', error);\n            return 0;\n        }\n    }\n    static async generateMatchExplanation(user1Profile, user2Profile, score) {\n        const prompt = `\n    解释为什么这两个用户的兼容性分数是${score}分：\n\n    用户1：${user1Profile.name}\n    用户2：${user2Profile.name}\n\n    请提供：\n    1. 匹配的优势（为什么他们适合）\n    2. 潜在的挑战（需要注意的地方）\n    3. 建议的相处方式\n    4. 发展前景预测\n\n    用温暖、专业的语调，给出建设性的建议。\n    `;\n        try {\n            const completion = await openrouter.chat.completions.create({\n                model: this.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ]\n            });\n            return completion.choices[0].message.content || '暂时无法生成匹配解释，请稍后重试。';\n        } catch (error) {\n            console.error('Error generating match explanation:', error);\n            return '暂时无法生成匹配解释，请稍后重试。';\n        }\n    }\n    static async generateComprehensiveAnalysis(user1Profile, user2Profile, score) {\n        const prompt = `\n    基于以下两个用户的资料，生成详细的匹配分析：\n\n    用户1：\n    ${JSON.stringify(user1Profile, null, 2)}\n\n    用户2：\n    ${JSON.stringify(user2Profile, null, 2)}\n\n    兼容性分数：${score}分\n\n    请生成结构化的分析报告，包含：\n    1. 详细的匹配解释\n    2. 3-5个匹配优势\n    3. 2-3个需要注意的挑战\n    4. 3-5个建议的聊天话题\n\n    返回JSON格式：\n    {\n      \"explanation\": \"详细的匹配解释文本\",\n      \"strengths\": [\"优势1\", \"优势2\", \"优势3\"],\n      \"challenges\": [\"挑战1\", \"挑战2\"],\n      \"suggestions\": [\"话题1\", \"话题2\", \"话题3\"]\n    }\n    `;\n        try {\n            const completion = await openrouter.chat.completions.create({\n                model: this.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ],\n                response_format: {\n                    type: 'json_object'\n                }\n            });\n            const content = completion.choices[0].message.content;\n            if (content) {\n                return JSON.parse(content);\n            }\n            // Fallback: create a structured response\n            return {\n                explanation: '基于双方的资料分析，你们在多个方面都有很好的契合度。',\n                strengths: [\n                    '价值观契合',\n                    '兴趣相投',\n                    '性格互补'\n                ],\n                challenges: [\n                    '需要更多了解',\n                    '沟通方式磨合'\n                ],\n                suggestions: [\n                    '分享兴趣爱好',\n                    '聊聊人生目标',\n                    '交流价值观'\n                ]\n            };\n        } catch (error) {\n            console.error('Error generating comprehensive analysis:', error);\n            // Return fallback data\n            return {\n                explanation: '暂时无法生成详细分析，请稍后重试。',\n                strengths: [\n                    '等待AI分析'\n                ],\n                challenges: [\n                    '等待AI分析'\n                ],\n                suggestions: [\n                    '等待AI分析'\n                ]\n            };\n        }\n    }\n    static async generateDatePlan(user1Profile, user2Profile) {\n        const prompt = `\n    基于以下两个用户的资料，为他们设计一个完美的约会计划：\n\n    用户1：\n    ${JSON.stringify(user1Profile, null, 2)}\n\n    用户2：\n    ${JSON.stringify(user2Profile, null, 2)}\n\n    请设计一个考虑双方兴趣爱好、性格特点和偏好的约会计划。包含：\n    1. 约会主题和理念\n    2. 具体的约会活动安排（时间线）\n    3. 推荐的地点类型\n    4. 注意事项和建议\n    5. 备选方案\n\n    返回JSON格式：\n    {\n      \"theme\": \"约会主题\",\n      \"concept\": \"约会理念描述\",\n      \"timeline\": [\n        {\n          \"time\": \"时间段\",\n          \"activity\": \"活动内容\",\n          \"location\": \"地点类型\",\n          \"reason\": \"选择理由\"\n        }\n      ],\n      \"recommendations\": {\n        \"locations\": [\"推荐地点1\", \"推荐地点2\", ...],\n        \"tips\": [\"建议1\", \"建议2\", ...],\n        \"alternatives\": [\"备选方案1\", \"备选方案2\", ...]\n      },\n      \"budget\": \"预算建议\",\n      \"duration\": \"约会时长\"\n    }\n    `;\n        try {\n            const completion = await openrouter.chat.completions.create({\n                model: this.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ],\n                response_format: {\n                    type: 'json_object'\n                }\n            });\n            const content = completion.choices[0].message.content;\n            if (content) {\n                return JSON.parse(content);\n            }\n            // Fallback: create a basic date plan\n            return {\n                theme: \"轻松愉快的初次约会\",\n                concept: \"选择轻松的环境，让双方都能感到舒适，有充分的交流机会。\",\n                timeline: [\n                    {\n                        time: \"下午2:00-3:30\",\n                        activity: \"咖啡厅聊天\",\n                        location: \"安静的咖啡厅\",\n                        reason: \"轻松的环境有利于深入交流\"\n                    },\n                    {\n                        time: \"下午3:30-5:00\",\n                        activity: \"公园散步\",\n                        location: \"附近的公园\",\n                        reason: \"自然环境能缓解紧张情绪\"\n                    }\n                ],\n                recommendations: {\n                    locations: [\n                        \"星巴克\",\n                        \"当地特色咖啡厅\",\n                        \"公园\",\n                        \"美术馆\"\n                    ],\n                    tips: [\n                        \"保持轻松的心态\",\n                        \"准备一些有趣的话题\",\n                        \"注意倾听\"\n                    ],\n                    alternatives: [\n                        \"如果天气不好可以选择室内活动\",\n                        \"可以根据聊天情况延长或缩短时间\"\n                    ]\n                },\n                budget: \"100-200元\",\n                duration: \"2-3小时\"\n            };\n        } catch (error) {\n            console.error('Error generating date plan:', error);\n            // Return fallback data\n            return {\n                theme: \"经典约会\",\n                concept: \"简单而美好的相遇。\",\n                timeline: [\n                    {\n                        time: \"下午\",\n                        activity: \"咖啡约会\",\n                        location: \"咖啡厅\",\n                        reason: \"轻松愉快的环境\"\n                    }\n                ],\n                recommendations: {\n                    locations: [\n                        \"咖啡厅\"\n                    ],\n                    tips: [\n                        \"保持自然\"\n                    ],\n                    alternatives: [\n                        \"灵活调整\"\n                    ]\n                },\n                budget: \"适中\",\n                duration: \"2小时\"\n            };\n        }\n    }\n    // 通用文本生成方法\n    static async generateText(prompt) {\n        try {\n            const completion = await openrouter.chat.completions.create({\n                model: this.model,\n                messages: [\n                    {\n                        role: 'user',\n                        content: prompt\n                    }\n                ],\n                temperature: 0.7,\n                max_tokens: 2000\n            });\n            return completion.choices[0]?.message?.content || '';\n        } catch (error) {\n            console.error('Error generating text:', error);\n            throw error;\n        }\n    }\n    // 生成个人资料\n    static async generateProfile(userInfo) {\n        const prompt = `\n    基于以下用户信息，生成一个真实、吸引人的个人资料：\n\n    用户信息：\n    - 姓名：${userInfo.name}\n    - 年龄：${userInfo.age}\n    - 性别：${userInfo.gender}\n    - 位置：${userInfo.location || '未提供'}\n    - 兴趣爱好：${userInfo.interests?.join(', ') || '未提供'}\n\n    请生成以下四个字段的内容，要求：\n    1. 内容要真实可信，不要过于夸张\n    2. 符合中国文化背景和表达习惯\n    3. 根据年龄和性别调整语言风格\n    4. 每个字段控制在合适的长度\n\n    返回JSON格式：\n    {\n      \"bio\": \"个人简介（50-100字，简洁介绍自己）\",\n      \"selfDescription\": \"自我描述（100-200字，详细描述性格、价值观、生活方式等）\",\n      \"lookingFor\": \"寻找对象（80-150字，描述理想伴侣的特质和期望）\",\n      \"relationshipGoals\": \"感情目标（50-100字，说明希望建立什么样的关系）\"\n    }\n\n    示例风格参考：\n    - 如果是年轻人（20-30岁）：语言活泼一些，提到学习、工作、兴趣爱好\n    - 如果是中年人（30-40岁）：语言成熟稳重，提到事业、家庭、人生规划\n    - 男性：可以提到责任感、上进心、兴趣爱好\n    - 女性：可以提到温柔、独立、生活品质\n\n    请确保内容积极正面，避免负面表达。\n    `;\n        try {\n            const result = await this.generateText(prompt);\n            // 尝试解析JSON\n            const cleanedResult = result.replace(/```json\\n?|\\n?```/g, '').trim();\n            const parsedResult = JSON.parse(cleanedResult);\n            // 验证返回的字段\n            if (!parsedResult.bio || !parsedResult.selfDescription || !parsedResult.lookingFor || !parsedResult.relationshipGoals) {\n                throw new Error('AI生成的内容格式不完整');\n            }\n            return parsedResult;\n        } catch (error) {\n            console.error('Error parsing AI response:', error);\n            // 如果AI生成失败，返回默认示例\n            return this.getDefaultProfileExample(userInfo);\n        }\n    }\n    // 默认个人资料示例\n    static getDefaultProfileExample(userInfo) {\n        const { name, age, gender } = userInfo;\n        // 根据性别和年龄生成不同的默认示例\n        if (gender === 'male') {\n            return {\n                bio: `我是${name}，${age}岁，一个积极向上的人。喜欢探索新事物，享受生活中的美好时刻。`,\n                selfDescription: `我是一个比较随和的人，喜欢和朋友聊天，也享受独处的时光。工作上比较认真负责，生活中喜欢尝试新的体验。我相信真诚和善良是最重要的品质，希望能遇到志同道合的人一起分享生活的点点滴滴。`,\n                lookingFor: `希望遇到一个善良、有趣的女生，我们可以一起聊天、一起探索这个世界。不需要完全相同的兴趣爱好，但希望我们能互相理解和支持。`,\n                relationshipGoals: `希望能建立一段真诚、稳定的关系，从朋友开始，慢慢了解彼此，看看是否适合走得更远。`\n            };\n        } else {\n            return {\n                bio: `我是${name}，${age}岁，一个热爱生活的女生。喜欢美好的事物，相信生活中处处都有小确幸。`,\n                selfDescription: `我是一个比较温和的人，喜欢和朋友分享生活中的趣事，也很享受安静的独处时光。我觉得保持好奇心很重要，总是愿意尝试新的事物。我重视真诚的交流，希望能遇到一个能够理解我、支持我的人。`,\n                lookingFor: `希望遇到一个成熟、有责任心的男生，我们可以一起成长，一起面对生活中的挑战。希望他有自己的兴趣爱好，也能尊重我的选择。`,\n                relationshipGoals: `希望能找到一个可以长期相伴的人，我们可以从了解开始，慢慢建立深厚的感情基础。`\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/gemini.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/matching.ts":
/*!**************************************!*\
  !*** ./src/lib/services/matching.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MatchingService: () => (/* binding */ MatchingService)\n/* harmony export */ });\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var _gemini__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./gemini */ \"(rsc)/./src/lib/services/gemini.ts\");\n\n\n\n\nclass MatchingService {\n    // 检查24小时内的匹配次数\n    static async checkDailyMatchLimit(userId) {\n        try {\n            // 计算24小时前的时间\n            const twentyFourHoursAgo = new Date();\n            twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);\n            // 查询24小时内用户创建的匹配数量\n            const recentMatches = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches.user1Id, userId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.gte)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches.createdAt, twentyFourHoursAgo)));\n            const matchCount = recentMatches.length;\n            const maxDailyMatches = 3;\n            const canMatch = matchCount < maxDailyMatches;\n            const remainingMatches = Math.max(0, maxDailyMatches - matchCount);\n            // 计算下次重置时间（最早匹配的24小时后）\n            let nextResetTime = new Date();\n            if (recentMatches.length > 0) {\n                const earliestMatch = recentMatches.reduce((earliest, match)=>new Date(match.createdAt || new Date()) < new Date(earliest.createdAt || new Date()) ? match : earliest);\n                nextResetTime = new Date(earliestMatch.createdAt || new Date());\n                nextResetTime.setHours(nextResetTime.getHours() + 24);\n            }\n            return {\n                canMatch,\n                remainingMatches,\n                nextResetTime\n            };\n        } catch (error) {\n            console.error('Error checking daily match limit:', error);\n            // 如果检查失败，默认允许匹配\n            return {\n                canMatch: true,\n                remainingMatches: 3,\n                nextResetTime: new Date()\n            };\n        }\n    }\n    static async findPotentialMatches(userId, limit = 10) {\n        // Get current user's profile\n        const currentUser = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users.id, userId)).limit(1);\n        if (!currentUser.length) {\n            throw new Error('User not found');\n        }\n        // Get all existing matches for current user (both as user1 and user2)\n        const existingMatches = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.or)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches.user1Id, userId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches.user2Id, userId)));\n        // Extract all matched user IDs\n        const existingMatchIds = new Set();\n        existingMatches.forEach((match)=>{\n            if (match.user1Id === userId) {\n                existingMatchIds.add(match.user2Id);\n            } else {\n                existingMatchIds.add(match.user1Id);\n            }\n        });\n        // Get current user's gender for opposite gender matching\n        const currentUserGender = currentUser[0].gender;\n        const targetGender = currentUserGender === 'male' ? 'female' : 'male';\n        // Find all potential matches (excluding current user, already matched users, and same gender)\n        const allPotentialMatches = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.and)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.ne)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users.id, userId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users.isActive, true), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users.gender, targetGender) // Only match opposite gender\n        ));\n        // Filter out already matched users\n        const availableMatches = allPotentialMatches.filter((user)=>!existingMatchIds.has(user.id));\n        // Randomize the results to avoid always matching the same users\n        const shuffled = availableMatches.sort(()=>Math.random() - 0.5);\n        // Return limited number of matches\n        return shuffled.slice(0, limit);\n    }\n    static async createMatch(user1Id, user2Id) {\n        try {\n            // Get both users' profiles\n            const [user1Data, user2Data] = await Promise.all([\n                this.getUserWithProfile(user1Id),\n                this.getUserWithProfile(user2Id)\n            ]);\n            if (!user1Data || !user2Data) {\n                throw new Error('One or both users not found');\n            }\n            // Generate AI analysis\n            const [personalitySummary1, personalitySummary2] = await Promise.all([\n                _gemini__WEBPACK_IMPORTED_MODULE_2__.GeminiService.generatePersonalitySummary({\n                    ...user1Data.user,\n                    ...user1Data.profile\n                }),\n                _gemini__WEBPACK_IMPORTED_MODULE_2__.GeminiService.generatePersonalitySummary({\n                    ...user2Data.user,\n                    ...user2Data.profile\n                })\n            ]);\n            // Calculate compatibility score\n            const compatibilityScore = await _gemini__WEBPACK_IMPORTED_MODULE_2__.GeminiService.calculateCompatibilityScore({\n                ...user1Data.user,\n                ...user1Data.profile,\n                personalitySummary: personalitySummary1\n            }, {\n                ...user2Data.user,\n                ...user2Data.profile,\n                personalitySummary: personalitySummary2\n            });\n            // Simulate conversation\n            const conversationSimulation = await _gemini__WEBPACK_IMPORTED_MODULE_2__.GeminiService.simulateConversation({\n                ...user1Data.user,\n                ...user1Data.profile,\n                personalitySummary: personalitySummary1\n            }, {\n                ...user2Data.user,\n                ...user2Data.profile,\n                personalitySummary: personalitySummary2\n            });\n            // Generate comprehensive match analysis\n            const matchAnalysis = await _gemini__WEBPACK_IMPORTED_MODULE_2__.GeminiService.generateComprehensiveAnalysis({\n                ...user1Data.user,\n                ...user1Data.profile\n            }, {\n                ...user2Data.user,\n                ...user2Data.profile\n            }, compatibilityScore);\n            // Generate date plan\n            const datePlan = await _gemini__WEBPACK_IMPORTED_MODULE_2__.GeminiService.generateDatePlan({\n                ...user1Data.user,\n                ...user1Data.profile\n            }, {\n                ...user2Data.user,\n                ...user2Data.profile\n            });\n            // Create match record\n            const matchData = {\n                user1Id,\n                user2Id,\n                compatibilityScore,\n                aiAnalysis: {\n                    user1PersonalitySummary: personalitySummary1,\n                    user2PersonalitySummary: personalitySummary2,\n                    explanation: matchAnalysis.explanation,\n                    strengths: matchAnalysis.strengths,\n                    challenges: matchAnalysis.challenges,\n                    suggestions: matchAnalysis.suggestions,\n                    datePlan: datePlan\n                },\n                conversationSimulation,\n                status: 'pending'\n            };\n            const [match] = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches).values(matchData).returning();\n            return {\n                match,\n                user1: user1Data.user,\n                user2: user2Data.user,\n                compatibilityScore,\n                explanation: matchAnalysis.explanation,\n                conversationSimulation\n            };\n        } catch (error) {\n            console.error('Error creating match:', error);\n            throw error;\n        }\n    }\n    static async getUserMatches(userId) {\n        const userMatches = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.or)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches.user1Id, userId), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches.user2Id, userId)));\n        const enrichedMatches = await Promise.all(userMatches.map(async (match)=>{\n            const otherUserId = match.user1Id === userId ? match.user2Id : match.user1Id;\n            const otherUserData = await this.getUserWithProfile(otherUserId);\n            return {\n                ...match,\n                otherUser: otherUserData?.user,\n                otherUserProfile: otherUserData?.profile\n            };\n        }));\n        return enrichedMatches;\n    }\n    static async updateMatchStatus(matchId, userId, liked) {\n        const [match] = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches.id, matchId)).limit(1);\n        if (!match) {\n            throw new Error('Match not found');\n        }\n        const isUser1 = match.user1Id === userId;\n        const updateData = {};\n        if (isUser1) {\n            updateData.user1Liked = liked;\n            updateData.user1Viewed = true;\n        } else {\n            updateData.user2Liked = liked;\n            updateData.user2Viewed = true;\n        }\n        // Check if both users have liked each other\n        const otherUserLiked = isUser1 ? match.user2Liked : match.user1Liked;\n        if (liked && otherUserLiked) {\n            updateData.status = 'mutual_like';\n        } else if (!liked) {\n            updateData.status = 'rejected';\n        }\n        const [updatedMatch] = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches).set(updateData).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.matches.id, matchId)).returning();\n        return updatedMatch;\n    }\n    static async getUserWithProfile(userId) {\n        const [user] = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.users.id, userId)).limit(1);\n        if (!user) return null;\n        const [profile] = await _lib_db__WEBPACK_IMPORTED_MODULE_0__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.userProfiles).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_3__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_1__.userProfiles.userId, userId)).limit(1);\n        return {\n            user,\n            profile: profile || null\n        };\n    }\n    static async generateDailyMatches(userId) {\n        try {\n            // 改为一次只生成一个匹配，减少 token 消耗\n            const potentialMatches = await this.findPotentialMatches(userId, 1);\n            const matches = [];\n            for (const potentialMatch of potentialMatches){\n                try {\n                    const match = await this.createMatch(userId, potentialMatch.id);\n                    matches.push(match);\n                } catch (error) {\n                    console.error(`Error creating match with user ${potentialMatch.id}:`, error);\n                // Continue with other matches even if one fails\n                }\n            }\n            return matches;\n        } catch (error) {\n            console.error('Error generating daily matches:', error);\n            throw error;\n        }\n    }\n    static async generateSingleMatch(userId) {\n        try {\n            // 检查24小时内的匹配限制\n            const limitCheck = await this.checkDailyMatchLimit(userId);\n            if (!limitCheck.canMatch) {\n                const error = new Error('DAILY_LIMIT_EXCEEDED');\n                error.limitInfo = limitCheck;\n                throw error;\n            }\n            // Find one potential match for the user\n            const potentialMatches = await this.findPotentialMatches(userId, 1);\n            if (potentialMatches.length === 0) {\n                return null; // No more potential matches\n            }\n            const match = await this.createMatch(userId, potentialMatches[0].id);\n            return match;\n        } catch (error) {\n            console.error('Error generating single match:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/matching.ts\n");

/***/ })

};
;