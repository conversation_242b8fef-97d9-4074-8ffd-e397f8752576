"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/camelcase";
exports.ids = ["vendor-chunks/camelcase"];
exports.modules = {

/***/ "(rsc)/./node_modules/camelcase/index.js":
/*!*****************************************!*\
  !*** ./node_modules/camelcase/index.js ***!
  \*****************************************/
/***/ ((module) => {

eval("\n\nconst UPPERCASE = /[\\p{Lu}]/u;\nconst LOWERCASE = /[\\p{Ll}]/u;\nconst LEADING_CAPITAL = /^[\\p{Lu}](?![\\p{Lu}])/gu;\nconst IDENTIFIER = /([\\p{Alpha}\\p{N}_]|$)/u;\nconst SEPARATORS = /[_.\\- ]+/;\n\nconst LEADING_SEPARATORS = new RegExp('^' + SEPARATORS.source);\nconst SEPARATORS_AND_IDENTIFIER = new RegExp(SEPARATORS.source + IDENTIFIER.source, 'gu');\nconst NUMBERS_AND_IDENTIFIER = new RegExp('\\\\d+' + IDENTIFIER.source, 'gu');\n\nconst preserveCamelCase = (string, toLowerCase, toUpperCase) => {\n\tlet isLastCharLower = false;\n\tlet isLastCharUpper = false;\n\tlet isLastLastCharUpper = false;\n\n\tfor (let i = 0; i < string.length; i++) {\n\t\tconst character = string[i];\n\n\t\tif (isLastCharLower && UPPERCASE.test(character)) {\n\t\t\tstring = string.slice(0, i) + '-' + string.slice(i);\n\t\t\tisLastCharLower = false;\n\t\t\tisLastLastCharUpper = isLastCharUpper;\n\t\t\tisLastCharUpper = true;\n\t\t\ti++;\n\t\t} else if (isLastCharUpper && isLastLastCharUpper && LOWERCASE.test(character)) {\n\t\t\tstring = string.slice(0, i - 1) + '-' + string.slice(i - 1);\n\t\t\tisLastLastCharUpper = isLastCharUpper;\n\t\t\tisLastCharUpper = false;\n\t\t\tisLastCharLower = true;\n\t\t} else {\n\t\t\tisLastCharLower = toLowerCase(character) === character && toUpperCase(character) !== character;\n\t\t\tisLastLastCharUpper = isLastCharUpper;\n\t\t\tisLastCharUpper = toUpperCase(character) === character && toLowerCase(character) !== character;\n\t\t}\n\t}\n\n\treturn string;\n};\n\nconst preserveConsecutiveUppercase = (input, toLowerCase) => {\n\tLEADING_CAPITAL.lastIndex = 0;\n\n\treturn input.replace(LEADING_CAPITAL, m1 => toLowerCase(m1));\n};\n\nconst postProcess = (input, toUpperCase) => {\n\tSEPARATORS_AND_IDENTIFIER.lastIndex = 0;\n\tNUMBERS_AND_IDENTIFIER.lastIndex = 0;\n\n\treturn input.replace(SEPARATORS_AND_IDENTIFIER, (_, identifier) => toUpperCase(identifier))\n\t\t.replace(NUMBERS_AND_IDENTIFIER, m => toUpperCase(m));\n};\n\nconst camelCase = (input, options) => {\n\tif (!(typeof input === 'string' || Array.isArray(input))) {\n\t\tthrow new TypeError('Expected the input to be `string | string[]`');\n\t}\n\n\toptions = {\n\t\tpascalCase: false,\n\t\tpreserveConsecutiveUppercase: false,\n\t\t...options\n\t};\n\n\tif (Array.isArray(input)) {\n\t\tinput = input.map(x => x.trim())\n\t\t\t.filter(x => x.length)\n\t\t\t.join('-');\n\t} else {\n\t\tinput = input.trim();\n\t}\n\n\tif (input.length === 0) {\n\t\treturn '';\n\t}\n\n\tconst toLowerCase = options.locale === false ?\n\t\tstring => string.toLowerCase() :\n\t\tstring => string.toLocaleLowerCase(options.locale);\n\tconst toUpperCase = options.locale === false ?\n\t\tstring => string.toUpperCase() :\n\t\tstring => string.toLocaleUpperCase(options.locale);\n\n\tif (input.length === 1) {\n\t\treturn options.pascalCase ? toUpperCase(input) : toLowerCase(input);\n\t}\n\n\tconst hasUpperCase = input !== toLowerCase(input);\n\n\tif (hasUpperCase) {\n\t\tinput = preserveCamelCase(input, toLowerCase, toUpperCase);\n\t}\n\n\tinput = input.replace(LEADING_SEPARATORS, '');\n\n\tif (options.preserveConsecutiveUppercase) {\n\t\tinput = preserveConsecutiveUppercase(input, toLowerCase);\n\t} else {\n\t\tinput = toLowerCase(input);\n\t}\n\n\tif (options.pascalCase) {\n\t\tinput = toUpperCase(input.charAt(0)) + input.slice(1);\n\t}\n\n\treturn postProcess(input, toUpperCase);\n};\n\nmodule.exports = camelCase;\n// TODO: Remove this for the next major release\nmodule.exports[\"default\"] = camelCase;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/camelcase/index.js\n");

/***/ })

};
;